export const CURRENCY_CONFIG = {
    code: 'NGN',
    symbol: '₦',
    locale: 'en-NG',
    vatRate: 0.075, // 7.5% VAT
    deliveryFee: 1500, // Base delivery fee in Naira
    minimumOrder: 5000, // Minimum order amount
    formatOptions: {
        style: 'currency',
        currency: 'NGN',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    },
    // Example prices for different categories
    defaultPrices: {
        starters: {
            min: 2000,
            max: 5000
        },
        mains: {
            min: 5000,
            max: 15000
        },
        drinks: {
            min: 1000,
            max: 3000
        }
    }
};

export const formatPrice = (amount) => {
    return new Intl.NumberFormat(CURRENCY_CONFIG.locale, CURRENCY_CONFIG.formatOptions).format(amount);
};

export const validatePrice = (price) => {
    const numPrice = Number(price);
    if (isNaN(numPrice) || numPrice < 0) {
        throw new Error(`Invalid price: ${price}. Price must be a positive number in Naira.`);
    }
    return numPrice;
};