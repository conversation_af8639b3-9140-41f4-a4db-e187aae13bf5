const session = require('express-session');
const RedisStore = require('connect-redis').default;
const redis = require('redis');

class SessionManager {
    constructor() {
        this.redisClient = redis.createClient({
            host: process.env.REDIS_HOST,
            port: process.env.REDIS_PORT,
            password: process.env.REDIS_PASSWORD,
            tls: process.env.NODE_ENV === 'production'
        });

        this.sessionConfig = {
            store: new RedisStore({ client: this.redisClient }),
            secret: process.env.SESSION_SECRET,
            name: 'sessionId', // Instead of default 'connect.sid'
            resave: false,
            saveUninitialized: false,
            cookie: {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict',
                maxAge: 24 * 60 * 60 * 1000, // 24 hours
                path: '/',
                domain: process.env.COOKIE_DOMAIN
            }
        };
    }

    getSessionMiddleware() {
        return session(this.sessionConfig);
    }

    async destroySession(sessionId) {
        return new Promise((resolve, reject) => {
            this.redisClient.del(`sess:${sessionId}`, (err) => {
                if (err) reject(err);
                resolve();
            });
        });
    }
}

module.exports = new SessionManager();