
/* Checkout Page Styles */
.checkout-body {
    background: linear-gradient(135deg, var(--background-color) 0%, #ffffff 100%);
}

.checkout-page {
    padding: var(--spacing-xl) 0;
    min-height: calc(100vh - 200px);
}

/* Glassmorphism Effect */
.glassmorphism {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-sm);
}

/* Checkout Container Layout */
.checkout-container {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

/* Form Sections */
.checkout-form {
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
}

.form-section {
    margin-bottom: var(--spacing-xl);
}

.section-subheading {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.25rem;
    margin-bottom: var(--spacing-lg);
    color: var(--secondary-color);
}

/* Delivery Options */
.delivery-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.option-card {
    position: relative;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.option-card:hover {
    border-color: var(--primary-color);
}

.option-card input[type="radio"] {
    position: absolute;
    opacity: 0;
}

.option-card input[type="radio"]:checked + .option-content {
    color: var(--primary-color);
}

.option-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Form Grid and Inputs */
.form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.input-group {
    position: relative;
    margin-bottom: var(--spacing-md);
}

.input-group.full-width {
    grid-column: 1 / -1;
}

.input-group input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    padding-left: calc(var(--spacing-md) + 20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.input-group input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}

.input-group i {
    position: absolute;
    left: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

/* Order Summary */
.order-summary {
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
    position: sticky;
    top: var(--spacing-xl);
}

.summary-items {
    margin-bottom: var(--spacing-lg);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.total-amount {
    display: flex;
    justify-content: space-between;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
    padding-top: var(--spacing-md);
    border-top: 2px solid var(--border-color);
}

/* Submit Button */
.submit-btn {
    width: 100%;
    padding: var(--spacing-md);
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    transition: var(--transition);
}

.submit-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

/* Animations */
.floating-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .checkout-container {
        grid-template-columns: 1fr 300px;
    }
}

@media (max-width: 768px) {
    .checkout-container {
        grid-template-columns: 1fr;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .delivery-options {
        grid-template-columns: 1fr;
    }
    
    .order-summary {
        position: static;
        margin-top: var(--spacing-lg);
    }
}
