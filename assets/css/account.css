.auth-container {
    min-height: calc(100vh - var(--header-height));
    display: flex;
    align-items: center;
    justify-content: center;
    padding: calc(var(--header-height) + var(--spacing-xl)) var(--spacing-md) var(--spacing-xl);
    background: linear-gradient(135deg, var(--background-color) 0%, var(--white) 100%);
}

.auth-wrapper {
    width: 100%;
    max-width: 480px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transform: translateY(0);
    opacity: 1;
    animation: slideUpFade 0.6s ease-out;
}

.auth-switcher {
    display: flex;
    position: relative;
    background: var(--background-color);
    padding: 0.5rem;
    margin-bottom: var(--spacing-md);
}

.switch-btn {
    flex: 1;
    padding: var(--spacing-sm);
    border: none;
    background: none;
    color: var(--text-color);
    font-weight: 500;
    cursor: pointer;
    position: relative;
    z-index: 1;
    transition: color var(--transition);
}

.switch-btn.active {
    color: var(--white);
}

.switch-indicator {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    width: calc(50% - 0.5rem);
    height: calc(100% - 1rem);
    background: var(--primary-color);
    border-radius: calc(var(--border-radius) / 2);
    transition: transform 0.3s ease;
}

.forms-wrapper {
    padding: var(--spacing-md);
}

.auth-form {
    display: none;
    opacity: 0;
    transform: translateX(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.auth-form.active {
    display: block;
    opacity: 1;
    transform: translateX(0);
}

.form-group.floating {
    position: relative;
    margin-bottom: var(--spacing-md);
}

.form-group.floating input {
    width: 100%;
    padding: var(--spacing-sm);
    padding-left: 2.5rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color var(--transition), box-shadow var(--transition);
}

.form-group.floating label {
    position: absolute;
    left: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    padding: 0 0.5rem;
    color: var(--text-light);
    pointer-events: none;
    transition: all var(--transition);
}

.form-group.floating input:focus,
.form-group.floating input:not(:placeholder-shown) {
    border-color: var(--primary-color);
    outline: none;
}

.form-group.floating input:focus + label,
.form-group.floating input:not(:placeholder-shown) + label {
    top: 0;
    transform: translateY(-50%) scale(0.85);
    background: var(--white);
    color: var(--primary-color);
}

.toggle-password {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    transition: color var(--transition);
}

.toggle-password:hover {
    color: var(--primary-color);
}

.btn-loader {
    display: none;
    width: 20px;
    height: 20px;
    border: 2px solid var(--white);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

.btn.loading .btn-text {
    display: none;
}

.btn.loading .btn-loader {
    display: inline-block;
}

@keyframes slideUpFade {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@media (max-width: var(--mobile)) {
    .auth-wrapper {
        margin: 0 var(--spacing-sm);
    }
    
    .forms-wrapper {
        padding: var(--spacing-sm);
    }
}
