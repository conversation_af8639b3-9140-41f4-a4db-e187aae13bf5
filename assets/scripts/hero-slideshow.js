// hero-slideshow.js

class HeroCarousel {
  constructor() {
      // Initialize properties
      this.currentSlide = 0;
      this.slides = document.querySelectorAll('.hero-slide');
      this.totalSlides = this.slides.length;
      this.autoPlayInterval = null;
      this.transitionInProgress = false;

      // Get navigation elements
      this.prevButton = document.querySelector('.carousel-prev');
      this.nextButton = document.querySelector('.carousel-next');
      this.dotsContainer = document.querySelector('.carousel-dots');

      // Initialize the carousel
      this.init();
      this.setupParallax(); // Add parallax setup here
  }

  init() {
      if (this.slides.length === 0) {
          console.error('No slides found');
          return;
      }

      this.preloadImages();
      this.createDots();
      this.addEventListeners();
      this.showSlide(0);
      this.startAutoPlay();

      // Add touch support
      this.addTouchSupport();
  }

  preloadImages() {
    this.slides.forEach((slide, index) => {
        const img = slide.querySelector('.hero-image');
        if (img) {
            console.log('Attempting to load image:', img.src);
            slide.classList.remove('active', 'image-loaded');
            img.classList.remove('loaded');

            const loadImage = () => {
                console.log('Successfully loaded image:', img.src);
                img.classList.add('loaded');
                slide.classList.add('image-loaded');
                if (index === 0) {
                    slide.classList.add('active');
                }
            };

            const handleError = () => {
                console.error(`Failed to load image ${index + 1}:`, img.src);
                // Set a fallback image
                img.src = '/assets/images/fallback.jpg';
                // Try loading the fallback
                img.removeEventListener('error', handleError);
                img.addEventListener('load', loadImage);
            };

            if (img.complete) {
                loadImage();
            } else {
                img.addEventListener('load', loadImage);
                img.addEventListener('error', handleError);
            }
        }
    });
  }
  createDots() {
      if (!this.dotsContainer) return;

      this.dotsContainer.innerHTML = '';
      for (let i = 0; i < this.totalSlides; i++) {
          const dot = document.createElement('button');
          dot.classList.add('carousel-dot');
          dot.setAttribute('aria-label', `Slide ${i + 1}`);
          if (i === 0) dot.classList.add('active'); // Set first dot active

          dot.addEventListener('click', () => {
              this.stopAutoPlay();
              this.showSlide(i);
              this.startAutoPlay();
          });
          this.dotsContainer.appendChild(dot);
      }
  }

  addEventListeners() {
      if (this.prevButton) {
          this.prevButton.addEventListener('click', () => {
              this.stopAutoPlay();
              this.prevSlide();
              this.startAutoPlay();
          });
      }

      if (this.nextButton) {
          this.nextButton.addEventListener('click', () => {
              this.stopAutoPlay();
              this.nextSlide();
              this.startAutoPlay();
          });
      }

      // Add keyboard navigation
      document.addEventListener('keydown', (e) => {
          if (e.key === 'ArrowLeft') {
              this.stopAutoPlay();
              this.prevSlide();
              this.startAutoPlay();
          } else if (e.key === 'ArrowRight') {
              this.stopAutoPlay();
              this.nextSlide();
              this.startAutoPlay();
          }
      });
  }

  addTouchSupport() {
      let touchStartX = 0;
      let touchEndX = 0;

      const handleTouchStart = (e) => {
          touchStartX = e.touches[0].clientX;
      };

      const handleTouchMove = (e) => {
          touchEndX = e.touches[0].clientX;
      };

      const handleTouchEnd = () => {
          const difference = touchStartX - touchEndX;
          if (Math.abs(difference) > 50) { // Minimum swipe distance
              if (difference > 0) {
                  this.nextSlide();
              } else {
                  this.prevSlide();
              }
          }
      };

      document.querySelector('.hero').addEventListener('touchstart', handleTouchStart);
      document.querySelector('.hero').addEventListener('touchmove', handleTouchMove);
      document.querySelector('.hero').addEventListener('touchend', handleTouchEnd);
  }

  showSlide(index) {
      if (this.transitionInProgress) return; // Prevent multiple transitions
      this.transitionInProgress = true;

      // Handle index out of bounds
      if (index >= this.totalSlides) index = 0;
      if (index < 0) index = this.totalSlides - 1;


      // Update slides
      this.slides.forEach(slide => slide.classList.remove('active'));
      this.slides[index].classList.add('active');

      // Update dots (if they exist)
      const dots = this.dotsContainer?.querySelectorAll('.carousel-dot'); // Optional chaining
      if (dots) {
          dots.forEach(dot => dot.classList.remove('active'));
          dots[index].classList.add('active');
      }

      this.currentSlide = index;

      // Reset transition flag after animation completes (adjust time if needed)
      setTimeout(() => {
          this.transitionInProgress = false;
      }, 600); // 600ms should match your CSS transition time
  }

  prevSlide() {
      this.showSlide(this.currentSlide - 1);
  }

  nextSlide() {
      this.showSlide(this.currentSlide + 1);
  }

  startAutoPlay() {
      this.stopAutoPlay(); // Clear any existing interval
      this.autoPlayInterval = setInterval(() => {
          this.nextSlide();
      }, 5000); // Change slide every 5 seconds (5000ms)
  }

  stopAutoPlay() {
      if (this.autoPlayInterval) {
          clearInterval(this.autoPlayInterval);
          this.autoPlayInterval = null;
      }
  }
  setupParallax() {
      window.addEventListener('scroll', () => {
          const scrolled = window.pageYOffset;
          // Apply parallax to the active slide's background
          const activeSlide = document.querySelector('.hero-slide.active');
          if (activeSlide) {
              const backgroundLayer = activeSlide.querySelector('.hero-background-layer');
              if (backgroundLayer) { // Check if it exists
                backgroundLayer.style.transform = `translateY(${scrolled * 0.5}px)`;
              }
          }

      });
  }
}

// Initialize carousel when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const carousel = new HeroCarousel();
});




