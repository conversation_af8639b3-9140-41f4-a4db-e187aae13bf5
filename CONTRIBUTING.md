# Contributing to Magic Menu

## Getting Started

1. Fork the repository
2. Create a feature branch
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. Make your changes
4. Run tests
   ```bash
   npm test
   ```
5. Submit a pull request

## Code Style

- Follow ESLint configuration
- Use meaningful variable names
- Comment complex logic
- Keep functions small and focused

## Commit Messages

Format: `type(scope): description`

Types:
- feat: New feature
- fix: Bug fix
- docs: Documentation
- style: Formatting
- refactor: Code restructuring
- test: Adding tests
- chore: Maintenance

Example:
```
feat(cart): add quantity validation
```

## Pull Request Process

1. Update documentation
2. Add tests for new features
3. Ensure CI passes
4. Get review from maintainers
5. Squash commits before merge

## Code Review Guidelines

- Check for test coverage
- Verify performance impact
- Ensure accessibility compliance
- Review security implications