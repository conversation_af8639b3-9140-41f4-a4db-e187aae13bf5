// Store.js - Central state management system

export class Store {
    constructor(initialState = {}) {
        this.state = initialState;
        this.listeners = new Map();
        this.reducers = new Map();
    }

    // Get current state
    getState() {
        return { ...this.state };
    }

    // Register a reducer for a specific slice of state
    registerReducer(sliceName, reducer) {
        this.reducers.set(sliceName, reducer);
        // Initialize state slice if it doesn't exist
        if (!this.state[sliceName]) {
            this.state[sliceName] = reducer(undefined, { type: '@@INIT' });
        }
    }

    // Dispatch an action
    async dispatch(action) {
        // Handle async actions
        if (action instanceof Function) {
            try {
                // Dispatch loading start
                this.dispatch({ type: `${action.name}/pending` });
                
                // Execute async action
                const result = await action(this.dispatch, this.getState.bind(this));
                
                // Dispatch success
                this.dispatch({ type: `${action.name}/fulfilled`, payload: result });
                
                return result;
            } catch (error) {
                // Dispatch error
                this.dispatch({ type: `${action.name}/rejected`, error });
                throw error;
            }
        }

        // Handle regular actions
        let stateChanged = false;
        const newState = { ...this.state };

        this.reducers.forEach((reducer, sliceName) => {
            const currentStateSlice = this.state[sliceName];
            const newStateSlice = reducer(currentStateSlice, action);

            if (currentStateSlice !== newStateSlice) {
                newState[sliceName] = newStateSlice;
                stateChanged = true;
            }
        });

        if (stateChanged) {
            this.state = newState;
            this.notify();
        }
    }

    // Subscribe to state changes
    subscribe(selector, callback) {
        const id = Symbol();
        this.listeners.set(id, { selector, callback });
        
        // Initial call
        callback(selector(this.state));
        
        // Return unsubscribe function
        return () => {
            this.listeners.delete(id);
        };
    }

    // Notify all listeners of state changes
    notify() {
        this.listeners.forEach(({ selector, callback }) => {
            callback(selector(this.state));
        });
    }
}
