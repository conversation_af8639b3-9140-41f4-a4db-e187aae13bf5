// cartStore.js - Cart-specific state management

import { CURRENCY_CONFIG } from '../config/currency.js';

// Action Types
export const CART_ACTIONS = {
    ADD_ITEM: 'cart/addItem',
    REMOVE_ITEM: 'cart/removeItem',
    UPDATE_QUANTITY: 'cart/updateQuantity',
    CLEAR_CART: 'cart/clearCart'
};

// Initial State
const initialState = {
    items: [],
    totals: {
        subtotal: 0,
        tax: 0,
        deliveryFee: CURRENCY_CONFIG.deliveryFee,
        total: 0
    },
    isLoading: false,
    error: null
};

// Selectors
export const cartSelectors = {
    getItems: state => state.cart.items,
    getTotals: state => state.cart.totals,
    getItemCount: state => state.cart.items.reduce((sum, item) => sum + item.quantity, 0),
    getIsLoading: state => state.cart.isLoading,
    getError: state => state.cart.error
};

// Reducer
export function cartReducer(state = initialState, action) {
    switch (action.type) {
        case CART_ACTIONS.ADD_ITEM: {
            const existingItemIndex = state.items.findIndex(
                item => item.id === action.payload.id
            );

            let newItems;
            if (existingItemIndex >= 0) {
                newItems = state.items.map((item, index) =>
                    index === existingItemIndex
                        ? { ...item, quantity: item.quantity + action.payload.quantity }
                        : item
                );
            } else {
                newItems = [...state.items, action.payload];
            }

            return {
                ...state,
                items: newItems,
                totals: calculateTotals(newItems)
            };
        }

        case CART_ACTIONS.REMOVE_ITEM:
            return {
                ...state,
                items: state.items.filter(item => item.id !== action.payload),
                totals: calculateTotals(state.items.filter(item => item.id !== action.payload))
            };

        case CART_ACTIONS.UPDATE_QUANTITY:
            return {
                ...state,
                items: state.items.map(item =>
                    item.id === action.payload.id
                        ? { ...item, quantity: action.payload.quantity }
                        : item
                ),
                totals: calculateTotals(state.items)
            };

        case CART_ACTIONS.CLEAR_CART:
            return {
                ...initialState
            };

        default:
            return state;
    }
}

// Helper function to calculate totals
function calculateTotals(items) {
    const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const tax = subtotal * CURRENCY_CONFIG.vatRate;
    
    return {
        subtotal,
        tax,
        deliveryFee: CURRENCY_CONFIG.deliveryFee,
        total: subtotal + tax + CURRENCY_CONFIG.deliveryFee
    };
}