import { CURRENCY_CONFIG, formatPrice, validatePrice } from './config/currency.js';
import { ComponentErrorBoundary } from './utils/ComponentErrorBoundary.js';
import { Loader } from './utils/Loader.js';
import { ApiErrorHandler } from './utils/ApiErrorHandler.js';
import Analytics from './services/Analytics.js';
import { CartManager } from './components.js';

class CheckoutManager {
    constructor() {
        this.errorBoundary = new ComponentErrorBoundary(this, {
            fallbackUI: `
                <div class="error-boundary-fallback critical">
                    <h3>Checkout Error</h3>
                    <p>We encountered an error processing your checkout. Please try again or contact support.</p>
                    <button onclick="window.location.reload()" class="retry-button">
                        Retry Checkout
                    </button>
                </div>
            `,
            onError: (error) => {
                Alert.show(ApiErrorHandler.getErrorMessage(error), 'error');
            }
        });
        this.setupAnalytics();
    }

    setupAnalytics() {
        if (!Analytics.consent) return;

        // Track checkout steps
        const checkoutSteps = document.querySelectorAll('.checkout-step');
        checkoutSteps.forEach(step => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        Analytics.track('checkout_step_viewed', {
                            step: step.dataset.step
                        });
                    }
                });
            }, { threshold: 0.5 });

            observer.observe(step);
        });

        // Track form interactions
        const form = document.querySelector('.checkout-form');
        if (form) {
            form.addEventListener('submit', this.handleCheckoutSubmit.bind(this));
        }
    }

    async processCheckout(formData) {
        const submitBtn = document.querySelector('.checkout-submit-btn');
        let loader;
        
        try {
            Loader.showButtonLoader(submitBtn, 'Processing...');
            
            loader = Loader.show({
                text: 'Processing your order...'
            });

            const response = await api.post('/api/checkout', formData);
            
            Alert.show('Order placed successfully!', 'success');
            window.location.href = `/confirmation.html?orderId=${response.orderId}`;
        } catch (error) {
            const errorMessage = ApiErrorHandler.handleError(error, 'process_checkout');
            
            if (error.status === 400) {
                // Handle validation errors
                this.handleValidationErrors(error.details);
            } else {
                Alert.show(errorMessage, 'error');
            }
        } finally {
            Loader.hideButtonLoader(submitBtn);
            if (loader) Loader.hide(loader);
        }
    }

    handleValidationErrors(errors) {
        Object.entries(errors).forEach(([field, message]) => {
            const input = document.querySelector(`[name="${field}"]`);
            if (input) {
                const formGroup = input.closest('.form-group');
                if (formGroup) {
                    showError(formGroup, message);
                }
            }
        });
    }

    async validateCheckout(data) {
        return this.errorBoundary.execute(async () => {
            // Validation logic here
        });
    }

    async handleCheckoutSubmit(e) {
        e.preventDefault();

        if (!Analytics.consent) return;

        const formData = new FormData(e.target);
        const orderData = {
            items: CartManager.getCartItems(),
            totals: CartManager.getTotals(),
            customer: {
                email: formData.get('email'),
                name: formData.get('name')
            }
        };

        try {
            const response = await this.processOrder(orderData);
            
            Analytics.trackEcommerce('purchase', {
                orderId: response.orderId,
                value: orderData.totals.total,
                tax: orderData.totals.tax,
                shipping: orderData.totals.shipping,
                items: orderData.items
            });
        } catch (error) {
            Analytics.track('checkout_error', {
                error: error.message
            });
        }
    }
}

export default new CheckoutManager();

document.addEventListener('DOMContentLoaded', function() {
    // Initialize animations
    initializeAnimations();
    
    // Form validation and handling
    const checkoutForm = document.querySelector('.checkout-form');
    const inputs = document.querySelectorAll('.input-group input');
    
    // Input formatting and validation
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            validateInput(this);
        });
    });
    
    // Add data-validate attributes to fields
    const fields = {
        'email': 'email',
        'phone': 'phone',
        'card-number': 'creditCard',
        'name': 'name'
    };
    
    Object.entries(fields).forEach(([id, type]) => {
        const field = document.getElementById(id);
        if (field) {
            field.setAttribute('data-validate', type);
        }
    });

    // Initialize form validation
    initializeForms();

    // Add custom validation for card fields
    const cardNumber = document.getElementById('card-number');
    if (cardNumber) {
        cardNumber.addEventListener('input', function() {
            this.value = formatCardNumber(this.value);
        });
    }

    const cardExpiry = document.getElementById('card-expiry');
    if (cardExpiry) {
        cardExpiry.addEventListener('input', function() {
            this.value = formatExpiry(this.value);
        });
    }
    
    // Form submission
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', function(e) {
            e.preventDefault();
            if (validateForm()) {
                processCheckout();
            }
        });
    }
});

// Input validation functions
function validateInput(input) {
    const value = input.value.trim();
    const inputGroup = input.closest('.input-group');
    
    if (!value) {
        showError(inputGroup, 'This field is required');
        return false;
    }
    
    switch(input.id) {
        case 'phone':
            return validatePhone(input);
        case 'card-number':
            return validateCardNumber(input);
        case 'card-expiry':
            return validateExpiry(input);
        case 'card-cvv':
            return validateCVV(input);
        default:
            clearError(inputGroup);
            return true;
    }
}

// Formatting functions
function formatCardNumber(value) {
    const cleaned = value.replace(/\D/g, '');
    const groups = cleaned.match(/(\d{1,4})/g);
    return groups ? groups.join(' ').slice(0, 19) : '';
}

function formatExpiry(value) {
    const cleaned = value.replace(/\D/g, '');
    if (cleaned.length >= 2) {
        return cleaned.slice(0, 2) + '/' + cleaned.slice(2, 4);
    }
    return cleaned;
}

// Validation helpers
function validatePhone(input) {
    const phoneRegex = /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/;
    return validateWithRegex(input, phoneRegex, 'Please enter a valid phone number');
}

function validateCardNumber(input) {
    const cardRegex = /^(\d{4}\s?){4}$/;
    return validateWithRegex(input, cardRegex, 'Please enter a valid card number');
}

function validateExpiry(input) {
    const expiryRegex = /^(0[1-9]|1[0-2])\/([0-9]{2})$/;
    return validateWithRegex(input, expiryRegex, 'Please enter a valid expiry date (MM/YY)');
}

function validateCVV(input) {
    const cvvRegex = /^[0-9]{3,4}$/;
    return validateWithRegex(input, cvvRegex, 'Please enter a valid CVV');
}

function validateWithRegex(input, regex, errorMessage) {
    const inputGroup = input.closest('.input-group');
    const isValid = regex.test(input.value.trim());
    
    if (!isValid) {
        showError(inputGroup, errorMessage);
        return false;
    }
    
    clearError(inputGroup);
    return true;
}

// Error handling
function showError(inputGroup, message) {
    clearError(inputGroup);
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;
    inputGroup.appendChild(errorDiv);
    inputGroup.classList.add('error');
}

function clearError(inputGroup) {
    const existingError = inputGroup.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
    inputGroup.classList.remove('error');
}

// Form validation
function validateForm() {
    let isValid = true;
    const requiredInputs = document.querySelectorAll('.input-group input[required]');
    
    requiredInputs.forEach(input => {
        if (!validateInput(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

// Checkout processing
function processCheckout() {
    const submitBtn = document.querySelector('.submit-btn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    
    // Simulate API call
    setTimeout(() => {
        window.location.href = 'confirmation.html';
    }, 2000);
}

// Animation initialization
function initializeAnimations() {
    const elements = document.querySelectorAll('.fade-in');
    elements.forEach((element, index) => {
        element.style.animationDelay = `${index * 0.1}s`;
    });
}

function validateOrderAmount(total) {
    try {
        const validatedTotal = validatePrice(total);
        if (validatedTotal < CURRENCY_CONFIG.minimumOrder) {
            throw new Error(`Minimum order amount is ${formatPrice(CURRENCY_CONFIG.minimumOrder)}`);
        }
        return true;
    } catch (error) {
        Alert.show(error.message, 'error');
        return false;
    }
}

function updateOrderSummary() {
    const cartItems = CartManager.getCartItems();
    try {
        const { subtotal, tax, deliveryFee, total } = CartManager.calculateTotals(cartItems);
        
        document.querySelector('.summary-subtotal').textContent = formatPrice(subtotal);
        document.querySelector('.summary-tax').textContent = formatPrice(tax);
        document.querySelector('.summary-delivery').textContent = formatPrice(deliveryFee);
        document.querySelector('.summary-total').textContent = formatPrice(total);
        
        // Update placeholder text for payment amount
        const amountInput = document.querySelector('#payment-amount');
        if (amountInput) {
            amountInput.placeholder = `Amount (min. ${formatPrice(CURRENCY_CONFIG.minimumOrder)})`;
        }
    } catch (error) {
        console.error('Error updating order summary:', error);
        Alert.show('Error calculating order total. Please try again.', 'error');
    }
}
