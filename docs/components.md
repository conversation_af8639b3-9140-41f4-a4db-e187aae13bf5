# Component Library

## UI Components

### Modal
Displays content in a modal dialog.

```javascript
import { Modal } from './components.js';

const modal = new Modal({
  title: 'Item Details',
  content: '<div>Modal content here</div>',
  onClose: () => console.log('Modal closed')
});

modal.open();
modal.close();
```

### CartSidebar
Manages the shopping cart sidebar.

```javascript
import { CartSidebar } from './components.js';

const cart = new CartSidebar({
  onItemRemove: (itemId) => handleItemRemove(itemId),
  onQuantityChange: (itemId, qty) => handleQuantityChange(itemId, qty)
});
```

### Alert
Displays temporary notifications.

```javascript
import { Alert } from './components.js';

Alert.success('Order placed successfully');
Alert.error('Failed to process payment');
```

## Form Components

### FormValidator
Validates form inputs with customizable rules.

```javascript
import { FormValidator } from './components.js';

const validator = new FormValidator({
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  phone: {
    required: true,
    minLength: 10
  }
});
```

## Utility Components

### Loader
Shows loading state during async operations.

```javascript
import { Loader } from './components.js';

const loader = new Loader();
loader.show();
await fetchData();
loader.hide();
```