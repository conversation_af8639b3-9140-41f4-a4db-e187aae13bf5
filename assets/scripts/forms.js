import { CURRENCY_CONFIG, formatPrice } from './config/currency.js';

const PRICE_ERRORS = {
    INVALID_FORMAT: `Please enter a valid amount in Naira (${CURRENCY_CONFIG.symbol})`,
    BELOW_MINIMUM: `Minimum order amount is ${formatPrice(CURRENCY_CONFIG.minimumOrder)}`,
    PAYMENT_REQUIRED: 'Please enter the payment amount in Naira',
};

function initializeForms() {
    // Update price-related placeholders
    document.querySelectorAll('input[type="number"][data-price-input]').forEach(input => {
        input.placeholder = `Enter amount in ${CURRENCY_CONFIG.symbol}`;
    });

    // Update price-related labels
    document.querySelectorAll('[data-price-label]').forEach(label => {
        const minPrice = label.dataset.minPrice || CURRENCY_CONFIG.minimumOrder;
        label.textContent = label.textContent.replace(
            '{min_amount}',
            formatPrice(minPrice)
        );
    });
}