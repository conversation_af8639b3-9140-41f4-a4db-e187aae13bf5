// home.js

export class HomePage {
    constructor() {
        //this.initializeHero(); // REMOVED: Now handled in HeroCarousel
        this.initializeTestimonials();
        this.initializeMenuCards();
    }

    // initializeHero() is removed. It's now in hero-slideshow.js

    initializeTestimonials() {
        const testimonialData = [
            {
                text: "Best food in town! The flavors are amazing and the service is outstanding.",
                author: "<PERSON>",
                rating: 5
            },
            {
                text: "Fresh ingredients and authentic taste. Highly recommended!",
                author: "<PERSON>",
                rating: 5
            },
            {
                text: "Great atmosphere and delicious food. Will definitely come back!",
                author: "<PERSON>",
                rating: 4
            }
        ];

        const slider = document.querySelector('.testimonials-wrapper');
        if (!slider) return;

        // Create testimonial cards
        testimonialData.forEach(testimonial => {
            const card = this.createTestimonialCard(testimonial);
            slider.appendChild(card);
        });

        // Initialize slider functionality
        let currentSlide = 0;
        const slides = slider.querySelectorAll('.testimonial-card');
        const totalSlides = slides.length;

        // Show initial slide
        this.showSlide(slides, currentSlide);

        // Add event listeners for controls
        const prevButton = document.querySelector('.slider-prev');
        const nextButton = document.querySelector('.slider-next');

        if (prevButton && nextButton) {
            prevButton.addEventListener('click', () => {
                currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
                this.showSlide(slides, currentSlide);
            });

            nextButton.addEventListener('click', () => {
                currentSlide = (currentSlide + 1) % totalSlides;
                this.showSlide(slides, currentSlide);
            });
        }

        // Auto-advance slides
        setInterval(() => {
            currentSlide = (currentSlide + 1) % totalSlides;
            this.showSlide(slides, currentSlide);
        }, 5000);
    }

    createTestimonialCard(testimonial) {
        const card = document.createElement('div');
        card.className = 'testimonial-card';
        card.innerHTML = `
            <div class="testimonial-content">
                <div class="testimonial-rating">
                    ${this.createRatingStars(testimonial.rating)}
                </div>
                <p class="testimonial-text">${testimonial.text}</p>
                <p class="testimonial-author">- ${testimonial.author}</p>
            </div>
        `;
        return card;
    }

    createRatingStars(rating) {
        return Array(5).fill('').map((_, index) =>
            `<i class="fas fa-star${index < rating ? '' : '-o'}"></i>`
        ).join('');
    }

    showSlide(slides, index) {
        slides.forEach((slide, i) => {
            slide.style.transform = `translateX(${100 * (i - index)}%)`;
            slide.style.opacity = i === index ? '1' : '0';
        });
    }

    initializeMenuCards() {
        const cards = document.querySelectorAll('.menu-card');

        cards.forEach(card => {
            // Add hover effect
            card.addEventListener('mouseenter', () => {
                card.querySelector('.card-image-wrapper img')?.classList.add('hover');
            });

            card.addEventListener('mouseleave', () => {
                card.querySelector('.card-image-wrapper img')?.classList.remove('hover');
            });

            // Add click handler
            card.addEventListener('click', (e) => {
                if (!e.target.classList.contains('btn')) {
                    const link = card.querySelector('a.btn');
                    if (link) link.click();
                }
            });
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize AOS (Animate On Scroll)
    AOS.init({
        duration: 800,
        offset: 100,
        once: true
    });

    // Initialize home page functionality
     if (document.querySelector('.hero')) { // Check if we are in index page before loading
        new HomePage();
    }
});