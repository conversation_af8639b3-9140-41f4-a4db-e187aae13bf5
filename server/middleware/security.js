const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');

const securityMiddleware = {
    // HTTPS redirect
    httpsRedirect: (req, res, next) => {
        if (!req.secure && process.env.NODE_ENV === 'production') {
            return res.redirect(301, `https://${req.headers.host}${req.url}`);
        }
        next();
    },

    // Rate limiting
    rateLimiter: rateLimit({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 100, // limit each IP to 100 requests per windowMs
        message: 'Too many requests from this IP, please try again later.',
        standardHeaders: true,
        legacyHeaders: false,
    }),

    // Speed limiter
    speedLimiter: slowDown({
        windowMs: 15 * 60 * 1000, // 15 minutes
        delayAfter: 50, // allow 50 requests per 15 minutes, then...
        delayMs: 500 // begin adding 500ms of delay per request
    }),

    // Helmet configuration
    helmetConfig: helmet({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                scriptSrc: ["'self'", "'nonce-{NONCE}'"],
                styleSrc: ["'self'", "'unsafe-inline'"],
                imgSrc: ["'self'", "data:", "https:"],
                connectSrc: ["'self'", "https://api.magicmenu.com"],
                frameSrc: ["'none'"],
                objectSrc: ["'none'"],
                upgradeInsecureRequests: []
            }
        },
        hsts: {
            maxAge: 31536000,
            includeSubDomains: true,
            preload: true
        }
    })
};

module.exports = securityMiddleware;