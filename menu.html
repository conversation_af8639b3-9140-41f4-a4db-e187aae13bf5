<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Magic Menu - Menu</title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/menu.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/favicon-16x16.png">
    <link rel="manifest" href="assets/images/site.webmanifest">
    <!-- Add AOS library for scroll animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
</head>
<body class="menu-page">

     <!-- Header -->
    <header class="header">
        <div class="container header-content">
            <a href="index.html" class="logo">
                <img src="assets/images/logo.png" alt="Magic Menu Logo">
                <span class="logo-text">Magic Menu</span>
            </a>
            <nav class="main-nav">
                <ul class="nav-list">
                    <li class="nav-item"><a href="index.html" class="nav-link">Home</a></li>
                    <li class="nav-item"><a href="menu.html" class="nav-link active">Menu</a></li>
                    <li class="nav-item"><a href="cart.html" class="nav-link">Order</a></li>
                    <li class="nav-item"><a href="account.html" class="nav-link">Account</a></li>
                    <li class="nav-item"><a href="contact.html" class="nav-link">Contact</a></li>
                </ul>
            </nav>
            <div class="cart-icon" id="cartIcon">
                <span class="cart-count">0</span>
                <i class="fas fa-shopping-cart"></i>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="menu-hero">
            <h1 class="menu-title" data-aos="fade-up">Our Menu</h1>
            <p class="menu-subtitle" data-aos="fade-up" data-aos-delay="200">Discover our delicious selection</p>
        </div>

        <div class="container menu-container">
            <aside class="menu-sidebar" data-aos="fade-right">
                <nav class="category-nav">
                    <ul class="category-list">
                        <li class="category-item">
                            <a href="#local" class="category-link active">
                                <i class="fas fa-drumstick-bite"></i>  <!-- Example icon -->
                                <span>Local Delights</span>
                            </a>
                            <ul class="sub-category-list">
                                <li><a href="#soups" class="category-link sub-category-link">Soups</a></li>
                                <li><a href="#stews" class="category-link sub-category-link">Stews</a></li>
                                <li><a href="#rice" class="category-link sub-category-link">Rice Dishes</a></li>
                                 <li><a href="#swallows" class="category-link sub-category-link">Swallows</a></li>
                            </ul>
                        </li>
                        <li class="category-item">
                            <a href="#international" class="category-link">
                                <i class="fas fa-globe"></i> <!-- Example icon -->
                                <span>International Flavors</span>
                            </a>
                             <ul class="sub-category-list">
                                <li><a href="#pizza" class="category-link sub-category-link">Pizza</a></li>
                                <li><a href="#pasta" class="category-link sub-category-link">Pasta</a></li>
                                <li><a href="#burgers" class="category-link sub-category-link">Burgers</a></li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </aside>

            <div class="menu-content">
                <!-- Local Delights Section -->
                <section id="local" class="menu-section" data-aos="fade-up">
                    <h2 class="section-heading">Local Delights</h2>

                    <!-- Soups -->
                    <section id="soups" class="menu-category" data-aos="fade-up">
                        <h3 class="category-heading">Soups</h3>
                        <div class="menu-grid">
                            <!-- Example Soup Item -->
                            <div class="menu-item-card" data-aos="fade-up"
                                 data-title="Egusi Soup"
                                 data-description="Delicious soup made with ground melon seeds, vegetables, and assorted meats."
                                 data-price="₦10.99"
                                 data-calories="450"
                                 data-protein="25g"
                                 data-carbs="15g"
                                 data-ingredients="Egusi Seeds, Spinach, Assorted Meats, Palm Oil, Spices">
                                <div class="card-image-wrapper">
                                    <img src="assets/images/egusi.jpg" alt="Egusi Soup" loading="lazy">
                                    <div class="card-overlay">
                                        <button class="quick-view-btn">Quick View</button>
                                    </div>
                                </div>
                                <div class="card-content">
                                    <h4 class="card-title">Egusi Soup</h4>
                                    <p class="card-description">Delicious soup with ground melon seeds.</p>
                                    <div class="card-footer">
                                        <span class="card-price">₦10.99</span>
                                        <button class="btn btn-primary add-to-cart-btn">Add to Cart</button>
                                    </div>
                                </div>
                            </div>

                            <!--  more soup items -->
                            <div class="menu-item-card" data-aos="fade-up"
                                 data-title="Afang Soup"
                                 data-description="A popular Nigerian soup made with Afang leaves, waterleaf, and assorted meats"
                                 data-price="₦12.00"
                                 data-calories="400"
                                 data-protein="30g"
                                 data-carbs="10g"
                                 data-ingredients="Afang leaves, Waterleaf,  Assorted Meats, Palm Oil, Spices">
                                <div class="card-image-wrapper">
                                    <img src="assets/images/afang.jpg" alt="Afang Soup" loading="lazy">
                                    <div class="card-overlay">
                                        <button class="quick-view-btn">Quick View</button>
                                    </div>
                                </div>
                                <div class="card-content">
                                    <h4 class="card-title">Afang Soup</h4>
                                    <p class="card-description">Traditional Nigerian soup.</p>
                                    <div class="card-footer">
                                        <span class="card-price">₦12.00</span>
                                        <button class="btn btn-primary add-to-cart-btn">Add to Cart</button>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </section>

                    <!-- Stews -->
                    <section id="stews" class="menu-category" data-aos="fade-up">
                        <h3 class="category-heading">Stews</h3>
                        <div class="menu-grid">
                            <!-- Example Stew Item -->
                           <div class="menu-item-card" data-aos="fade-up"
                                 data-title="Ofada Stew"
                                 data-description="A local stew made with a mix of peppers, Iru and palm oil"
                                 data-price="₦14.00"
                                 data-calories="500"
                                 data-protein="20g"
                                 data-carbs="20g"
                                 data-ingredients="Assorted meat, Iru, Pepper, Palm Oil, Spices">
                                <div class="card-image-wrapper">
                                    <img src="assets/images/ofada-stew.jpg" alt="Ofada Stew" loading="lazy">
                                    <div class="card-overlay">
                                        <button class="quick-view-btn">Quick View</button>
                                    </div>
                                </div>
                                <div class="card-content">
                                    <h4 class="card-title">Ofada Stew</h4>
                                    <p class="card-description">Local stew made with peppers and Iru.</p>
                                    <div class="card-footer">
                                        <span class="card-price">₦14.00</span>
                                        <button class="btn btn-primary add-to-cart-btn">Add to Cart</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                   <!-- Rice Dishes -->
                    <section id="rice" class="menu-category" data-aos="fade-up">
                        <h3 class="category-heading">Rice Dishes</h3>
                        <div class="menu-grid">
                           <div class="menu-item-card" data-aos="fade-up"
                                 data-title="Jollof Rice"
                                 data-description="West African rice dish cooked in a flavorful tomato and pepper sauce."
                                 data-price="₦14.00"
                                 data-calories="600"
                                 data-protein="15g"
                                 data-carbs="80g"
                                 data-ingredients="Rice, Tomato Sauce, Pepper, Assorted meats, Spices">
                                <div class="card-image-wrapper">
                                    <img src="assets/images/jollof-rice.jpg" alt="Jollof Rice" loading="lazy">
                                    <div class="card-overlay">
                                        <button class="quick-view-btn">Quick View</button>
                                    </div>
                                </div>
                                <div class="card-content">
                                    <h4 class="card-title">Jollof Rice</h4>
                                    <p class="card-description">Classic West African rice dish.</p>
                                    <div class="card-footer">
                                        <span class="card-price">₦9.99</span>
                                        <button class="btn btn-primary add-to-cart-btn">Add to Cart</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                      <!-- Swallows -->
                    <section id="swallows" class="menu-category" data-aos="fade-up">
                        <h3 class="category-heading">Swallows</h3>
                        <div class="menu-grid">
                            <!-- Example Swallow Item -->
                            <div class="menu-item-card" data-aos="fade-up"
                                 data-title="Amala"
                                 data-description="A swallow dish made from yam or unripe plantain"
                                 data-price="₦7.00"
                                 data-calories="350"
                                 data-protein="5g"
                                 data-carbs="70g"
                                 data-ingredients="Yam Flour, Plantain Flour">
                                <div class="card-image-wrapper">
                                    <img src="assets/images/amala.jpg" alt="Amala" loading="lazy">
                                    <div class="card-overlay">
                                        <button class="quick-view-btn">Quick View</button>
                                    </div>
                                </div>
                                <div class="card-content">
                                    <h4 class="card-title">Amala</h4>
                                    <p class="card-description">Swallow dish.</p>
                                    <div class="card-footer">
                                        <span class="card-price">₦7.00</span>
                                        <button class="btn btn-primary add-to-cart-btn">Add to Cart</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Add more swallow items here -->

                        </div>
                    </section>
                </section>

                <!-- International Flavors Section -->
                <section id="international" class="menu-section" data-aos="fade-up">
                    <h2 class="section-heading">International Flavors</h2>

                       <!-- Pizza Section -->
                <section id="pizza" class="menu-category" data-aos="fade-up">
                    <h2 class="section-heading">Pizza</h2>
                    <div class="menu-grid">
                        <div class="menu-item-card" data-aos="fade-up"
                             data-id="pizza1"
                             data-title="Margherita Pizza"
                             data-description="Classic pizza with tomato sauce, fresh mozzarella, and basil leaves."
                             data-price="7500"
                             data-variations='{
                                 "size": {
                                     "label": "Size",
                                     "required": true,
                                     "options": [
                                         {"label": "Medium (12\")", "price": "7500"},
                                         {"label": "Large (14\")", "price": "9500"},
                                         {"label": "Extra Large (16\")", "price": "12500"}
                                     ]
                                 }
                             }'
                             data-calories="850"
                             data-protein="35g"
                             data-carbs="90g"
                             data-ingredients="Pizza Dough, Tomato Sauce, Fresh Mozzarella, Fresh Basil, Extra Virgin Olive Oil">
                            <div class="card-image-wrapper">
                                <img src="assets/images/pizza1.jpg" alt="Margherita Pizza" loading="lazy">
                                <div class="card-overlay">
                                    <button class="quick-view-btn">Quick View</button>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">Margherita Pizza</h3>
                                <p class="card-description">Classic pizza with tomato sauce, mozzarella, and basil.</p>
                                <div class="card-footer">
                                    <span class="card-price">₦12.99</span>
                                    <button class="btn btn-primary add-to-cart-btn">
                                        <i class="fas fa-cart-plus"></i>
                                        Add to Cart
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="menu-item-card" data-aos="fade-up"
                             data-title="Pepperoni Pizza"
                             data-description="Traditional pizza topped with spicy pepperoni slices."
                             data-price="₦14.99"
                             data-calories="950"
                             data-protein="40g"
                             data-carbs="95g"
                             data-ingredients="Pizza Dough, Tomato Sauce, Mozzarella, Pepperoni">
                            <div class="card-image-wrapper">
                                <img src="assets/images/pizza2.jpg" alt="Pepperoni Pizza" loading="lazy">
                                <div class="card-overlay">
                                    <button class="quick-view-btn">Quick View</button>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">Pepperoni Pizza</h3>
                                <p class="card-description">Traditional pizza topped with spicy pepperoni slices.</p>
                                <div class="card-footer">
                                    <span class="card-price">₦14.99</span>
                                    <button class="btn btn-primary add-to-cart-btn">
                                        <i class="fas fa-cart-plus"></i>
                                        Add to Cart
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Pasta Section -->
                <section id="pasta" class="menu-category" data-aos="fade-up">
                    <h2 class="section-heading">Pasta</h2>
                    <div class="menu-grid">
                        <div class="menu-item-card" data-aos="fade-up" data-title="Spaghetti Carbonara"
                             data-description="Creamy pasta with pancetta, egg, and parmesan cheese."
                             data-price="₦15.99"
                             data-calories="700"
                             data-protein="30g"
                             data-carbs="80g"
                             data-ingredients="Spaghetti, Pancetta, Eggs, Parmesan Cheese, Black Pepper">
                            <div class="card-image-wrapper">
                                <img src="assets/images/pasta1.jpg" alt="Spaghetti Carbonara" loading="lazy">
                                <div class="card-overlay">
                                    <button class="quick-view-btn">Quick View</button>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">Spaghetti Carbonara</h3>
                                <p class="card-description">Creamy pasta with pancetta, egg, and parmesan cheese.</p>
                                <div class="card-footer">
                                    <span class="card-price">₦15.99</span>
                                    <button class="btn btn-primary add-to-cart-btn">
                                        <i class="fas fa-cart-plus"></i>
                                        Add to Cart
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="menu-item-card" data-aos="fade-up"
                         data-title="Fettuccine Alfredo"
                         data-description="Rich and creamy fettuccine pasta in Alfredo sauce."
                         data-price="₦14.99"
                         data-calories="650"
                         data-protein="25g"
                         data-carbs="70g"
                         data-ingredients="Fettuccine, Butter, Heavy Cream, Parmesan Cheese, Garlic">
                            <div class="card-image-wrapper">
                                <img src="assets/images/pasta2.jpg" alt="Fettuccine Alfredo" loading="lazy">
                                <div class="card-overlay">
                                    <button class="quick-view-btn">Quick View</button>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">Fettuccine Alfredo</h3>
                                <p class="card-description">Rich and creamy fettuccine pasta in Alfredo sauce.</p>
                                <div class="card-footer">
                                    <span class="card-price">₦14.99</span>
                                    <button class="btn btn-primary add-to-cart-btn">
                                        <i class="fas fa-cart-plus"></i>
                                        Add to Cart
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Burgers Section -->
                <section id="burgers" class="menu-category" data-aos="fade-up">
                    <h2 class="section-heading">Burgers</h2>
                    <div class="menu-grid">
                        <div class="menu-item-card" data-aos="fade-up"
                             data-title="Classic Cheeseburger"
                             data-description="Juicy beef patty with cheese, lettuce, and tomato."
                             data-price="₦11.99"
                             data-calories="550"
                             data-protein="30g"
                             data-carbs="40g"
                             data-ingredients="Beef Patty, Cheese, Lettuce, Tomato, Bun">
                            <div class="card-image-wrapper">
                                <img src="assets/images/burger1.jpg" alt="Classic Cheeseburger" loading="lazy">
                                <div class="card-overlay">
                                    <button class="quick-view-btn">Quick View</button>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">Classic Cheeseburger</h3>
                                <p class="card-description">Juicy beef patty with cheese, lettuce, and tomato.</p>
                                <div class="card-footer">
                                    <span class="card-price">₦11.99</span>
                                    <button class="btn btn-primary add-to-cart-btn">
                                        <i class="fas fa-cart-plus"></i>
                                        Add to Cart
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="menu-item-card" data-aos="fade-up"
                            data-title="Bacon Deluxe Burger"
                            data-description="Premium beef patty topped with crispy bacon and special sauce."
                            data-price="₦13.99"
                            data-calories="700"
                            data-protein="40g"
                            data-carbs="45g"
                            data-ingredients="Beef Patty, Bacon, Special Sauce, Cheese, Lettuce, Tomato, Bun">
                            <div class="card-image-wrapper">
                                <img src="assets/images/burger2.jpg" alt="Bacon Deluxe Burger" loading="lazy">
                                <div class="card-overlay">
                                    <button class="quick-view-btn">Quick View</button>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">Bacon Deluxe Burger</h3>
                                <p class="card-description">Premium beef patty topped with crispy bacon and special sauce.</p>
                                <div class="card-footer">
                                    <span class="card-price">₦13.99</span>
                                    <button class="btn btn-primary add-to-cart-btn">
                                        <i class="fas fa-cart-plus"></i>
                                        Add to Cart
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- Item Detail Modal -->
    <div id="itemDetailModal" class="modal">
        <div class="modal-content">
            <span class="close-modal">×</span>
            <div class="item-detail-container">
                <div class="item-detail-image">
                    <img src="" alt="" id="modalImage">
                </div>
                <div class="item-detail-info">
                    <h2 id="modalTitle"></h2>
                    <p id="modalDescription"></p>
                    <div class="item-detail-meta">
                        <div class="nutrition-info">
                            <h3>Nutrition Information</h3>
                            <ul>
                                <li>Calories: <span id="modalCalories"></span></li>
                                <li>Protein: <span id="modalProtein"></span></li>
                                <li>Carbs: <span id="modalCarbs"></span></li>
                            </ul>
                        </div>
                        <div class="ingredients">
                            <h3>Ingredients</h3>
                            <p id="modalIngredients"></p>
                        </div>
                    </div>
                    <div class="item-detail-footer">
                        <div class="price" id="modalPrice"></div>
                        <div class="quantity-selector">
                            <button class="quantity-btn decrease">-</button>
                            <input type="number" value="1" min="1" max="10" id="modalQuantity">
                            <button class="quantity-btn increase">+</button>
                        </div>
                        <button class="btn btn-primary add-to-cart-btn" id="modalAddToCart">
                            <i class="fas fa-cart-plus"></i>
                            Add to Cart
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
     <!-- Footer -->
    <footer class="footer">
        <div class="container footer-content">
            <div class="footer-section">
                <h4 class="footer-heading">Contact</h4>
                <p>123 Main Street<br>City, State 12345</p>
                <p>Phone: (*************</p>
                <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
            <div class="footer-section">
                <h4 class="footer-heading">Hours</h4>
                <p>Mon-Fri: 11am - 10pm</p>
                <p>Sat-Sun: 12pm - 11pm</p>
            </div>
            <div class="footer-section">
                <h4 class="footer-heading">Links</h4>
                <ul class="footer-links"> <!-- Added class -->
                    <li><a href="terms.html" class="footer-link">Terms & Conditions</a></li> <!--Added class -->
                    <li><a href="privacy.html" class="footer-link">Privacy Policy</a></li>  <!--Added class -->
                    <li><a href="faq.html" class="footer-link">FAQ</a></li>  <!--Added class -->
                </ul>
            </div>
           <div class="footer-section">
                <h4 class="footer-heading">Follow Us</h4>
                <div class="social-links">
                    <a href="#" aria-label="facebook"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" aria-label="twitter"><i class="fab fa-twitter"></i></a>
                    <a href="#" aria-label="instagram"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
        </div>
        <div class="copyright">
            © 2025 Magic Menu. All rights reserved.
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script type="module" src="assets/scripts/api.js"></script>
    <script type="module">
        // Initialize AOS
        AOS.init({
            duration: 800,
            offset: 100,
            once: true
        });
    </script>
</body>
</html>
