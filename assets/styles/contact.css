.hero-section {
    position: relative;
    height: 300px;
    background-color: #f8f9fa; /* Fallback color */
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.map-error {
    background: #f8f9fa;
    padding: 2rem;
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.map-error p {
    margin: 0;
    color: #6c757d;
}
