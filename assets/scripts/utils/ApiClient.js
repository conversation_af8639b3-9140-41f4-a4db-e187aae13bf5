import { ApiErrorHandler, ApiError } from './ApiErrorHandler.js';

export class ApiClient {
    constructor(baseURL = 'https://api.magicmenu.com') {
        this.baseURL = baseURL;
        this.defaultTimeout = 30000; // 30 seconds
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
    }

    async request(endpoint, options = {}) {
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), options.timeout || this.defaultTimeout);

        try {
            // Security headers
            const securityHeaders = {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Authorization': `Bearer ${this.getApiKey()}`,
                'X-CSRF-Token': this.csrfToken,
                // Prevent browsers from interpreting files as a different MIME type
                'X-Content-Type-Options': 'nosniff',
                // Strict Transport Security
                'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
            };

            const response = await fetch(`${this.baseURL}${endpoint}`, {
                ...options,
                headers: {
                    ...securityHeaders,
                    ...options.headers,
                },
                credentials: 'same-origin', // Include cookies for same-origin requests
                signal: controller.signal
            });

            return await ApiErrorHandler.handleResponse(response);
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new ApiError('Request timeout', 'TIMEOUT', 408);
            }
            throw error;
        } finally {
            clearTimeout(timeout);
        }
    }

    async get(endpoint, options = {}) {
        return this.request(endpoint, { ...options, method: 'GET' });
    }

    async post(endpoint, data, options = {}) {
        return this.request(endpoint, {
            ...options,
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    async put(endpoint, data, options = {}) {
        return this.request(endpoint, {
            ...options,
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    async delete(endpoint, options = {}) {
        return this.request(endpoint, { ...options, method: 'DELETE' });
    }

    getApiKey() {
        // Get API key from secure HttpOnly cookie instead of localStorage
        return document.cookie.match(/apiKey=([^;]+)/)?.[1] || '';
    }

    // Method to sanitize data before sending to API
    sanitizeData(data) {
        if (typeof data !== 'object' || data === null) return data;
        
        const sanitized = {};
        for (const [key, value] of Object.entries(data)) {
            if (typeof value === 'string') {
                sanitized[key] = this.sanitizeString(value);
            } else if (typeof value === 'object' && value !== null) {
                sanitized[key] = this.sanitizeData(value);
            } else {
                sanitized[key] = value;
            }
        }
        return sanitized;
    }

    sanitizeString(str) {
        const div = document.createElement('div');
        div.textContent = str;
        return div.innerHTML;
    }
}

export const api = new ApiClient();
