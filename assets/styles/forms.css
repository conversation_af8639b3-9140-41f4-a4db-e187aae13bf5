.form-group, .input-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-group input,
.input-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: border-color 0.3s ease;
}

.form-group.has-error input,
.input-group.has-error input {
    border-color: #dc3545;
    background-color: #fff8f8;
}

.form-group.has-success input,
.input-group.has-success input {
    border-color: #28a745;
    background-color: #f8fff8;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
}

.error-message::before {
    content: "⚠";
    margin-right: 0.5rem;
}

.success-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #28a745;
    font-size: 1.2rem;
}

/* Animation */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

/* Real-time validation feedback */
.form-group input:focus,
.input-group input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
}

.form-group.has-error input:focus,
.input-group.has-error input:focus {
    box-shadow: 0 0 0 3px rgba(220,53,69,0.25);
}

.form-group.has-success input:focus,
.input-group.has-success input:focus {
    box-shadow: 0 0 0 3px rgba(40,167,69,0.25);
}