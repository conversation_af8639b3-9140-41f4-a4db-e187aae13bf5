/* 1. CSS Custom Properties (Variables) */
:root {
  /* Colors */
  --primary-color: #ff7a00;
  --primary-dark: #e66d00;
  --secondary-color: #2c3e50;
  --text-color: #333;
  --text-light: #666;
  --white: #ffffff;
  --background-color: #f8f9fa;
  --border-color: #ddd;
  --light-gray: #ccc; /* Added light gray */

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
   --spacing-xxl: 4rem; /* Added for very large spaces */

  /* Layout */
  --container-max: 1200px;
  --container-padding: 1rem;
  --border-radius: 8px;
  --header-height: 65px; /* Example value, adjust as needed */

  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 2px 15px rgba(0, 0, 0, 0.1);

  /* Transitions */
  --transition: all 0.3s ease;

  /* Breakpoints */
  --mobile: 480px;
  --tablet: 768px;
  --desktop: 1024px;
}

/* 2. Reset & Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}
/* Prevent body scrolling when modal is open*/
body.modal-open {
  overflow: hidden;
}

/* 3. Layout Components */
.container {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* 4. Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
  z-index: 1000;
  transition: transform 0.3s ease;
  height: 65px; /* Reduced from 80px */
}

/* Add this class when scrolling down */
.header-hidden {
  transform: translateY(-100%);
}
.header-scrolled {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) 0; /* Reduced padding */
}

.logo {
  font-size: 1.1rem; /* Slightly reduced */
  font-weight: 700;
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.logo img {
  height: 55px; /* Reduced from 70px */
  width: auto;
  object-fit: contain;
}

.logo-text {
  font-size: 1.2rem;
  white-space: nowrap;
}

/* 5. Navigation */
.main-nav {
  display: flex;
  align-items: center;
}

.nav-list {
  display: flex;
  gap: var(--spacing-lg);
  list-style: none;
}

.nav-link {
  color: var(--secondary-color);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: var(--transition);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* Mobile Navigation */
.mobile-menu-toggle {
  display: none; /* Hidden by default */
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
  z-index: 100; /* Ensure it's above the nav */
}

.mobile-menu-toggle span {
  display: block;
  width: 25px;
  height: 3px;
  background-color: var(--secondary-color);
  margin: 5px 0;
  transition: var(--transition);
}

@media (max-width: var(--tablet)) {
  .main-nav {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100vh;
      background-color: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      z-index: 99;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
  }

  .nav-list {
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-md);

  }

  .header.nav-open .main-nav {
      transform: translateX(0);
  }

  .mobile-menu-toggle {
      display: block;
  }

  /* X Icon Styles */
  .header.nav-open .mobile-menu-toggle span:nth-child(1) {
      transform: translateY(8px) rotate(45deg);
  }

  .header.nav-open .mobile-menu-toggle span:nth-child(2) {
      opacity: 0;
  }

  .header.nav-open .mobile-menu-toggle span:nth-child(3) {
      transform: translateY(-8px) rotate(-45deg);
  }
  /* Ensure the toggle button stays on top*/
  .header.nav-open .mobile-menu-toggle {
     position: relative; /* Make sure it stacks correctly */
      z-index: 100;
  }

  .nav-list {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .nav-item {
    margin: 0;
    padding: var(--spacing-xs) 0;
    text-align: center;
    width: 100%;
  }

  .nav-item:not(:last-child) {
    border-bottom: 1px solid var(--border-color);
  }
}

/* 6. Cart Components */
.cart-icon {
  position: relative;
  cursor: pointer;
  padding: var(--spacing-xs); /* Add some padding */
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--primary-color);
  color: var(--white);
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 50%;
  min-width: 18px; /* Ensure it's visible even with 0 items */
  text-align: center; /* Center the number */
}

/* 7. Footer */
.footer {
  position: relative;
  background-color: var(--secondary-color);
  color: var(--white);
  padding: var(--spacing-xl) 0 var(--spacing-md);
  margin-top: auto; /* Push footer to the bottom */
  width: 100%;
}
.footer-content {
  position: relative;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.footer-section h4 {
  margin-bottom: var(--spacing-sm);
}

.footer-links {
  list-style: none;
}

.footer-link {
  color: var(--white);
  text-decoration: none;
  opacity: 0.8;
  transition: var(--transition);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.footer-link:hover {
  opacity: 1;
}

/* 8. Responsive Design */
@media (max-width: var(--mobile)) {
  :root {
      --spacing-lg: 1.5rem;
      --spacing-xl: 2rem;
  }
}

@media (max-width: var(--tablet)) {
  .footer-content {
      grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: var(--desktop)) {
  .container {
      padding: 0 var(--spacing-md);
  }
}

/* 9. Utility Classes */
.text-center { text-align: center; }
.hidden { display: none; }
.visible { display: block; }
.flex { display: flex; }
.grid { display: grid; }
/* Add a utility class for input errors */
.input-error {
  border-color: red !important; /* Use !important to override if needed */
}
/*  Style for error messages within form groups/input groups */
.form-group .error-message,
.input-group .error-message {
  color: red;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}


/* Hero Section */
.hero {
  position: relative;
  height: 60vh;  /* Adjusted height */
  min-height: 700px; /* Adjusted min-height */
  overflow: hidden;
  background-color: #000;
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.6s ease-in-out;
  z-index: 1;
  pointer-events: none; /* Prevent clicks on inactive slides */
}

.hero-slide.active {
  opacity: 1;
  z-index: 2;
  pointer-events: auto;
}

.hero-background-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
   transition: transform 0.5s ease; /* Add a smooth transition */
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.6) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
  z-index: 2;
}

.hero-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  z-index: 1;
}

/* Class to add when image is loaded */
.hero-image.loaded {
  opacity: 1;
}

/* Ensure hero content stays above images */
.hero-content {
  position: relative;
  z-index: 5;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: var(--white);
  padding: var(--spacing-xl) 0;
}

/* Loading indicator */
.hero-slide:not(.image-loaded)::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 50px;
  height: 50px;
  margin-top: -25px;
  margin-left: -25px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
  z-index: 10;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.hero-text {
  max-width: 600px;
  padding: 0 var(--spacing-md);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5),
               0 0 10px rgba(0, 0, 0, 0.3);
}

.hero-heading {
  font-size: 3.5rem;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
  font-weight: 700;
  color: var(--white);
  position: relative;
  /* Optional: Add subtle outline */
  -webkit-text-stroke: 1px rgba(0, 0, 0, 0.1);
}

.hero-subheading {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.95;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

/* Optional: Add a dark backdrop behind the text */
.hero-text::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: radial-gradient(
    circle at center,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0) 70%
  );
  z-index: -1;
  pointer-events: none;
}

.hero-interactive {
  margin-top: var(--spacing-xl);
}

.hero-interactive-list {
  display: flex;
  gap: var(--spacing-lg);
  list-style: none;
}

.hero-interactive-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--white);
  text-decoration: none;
  transition: var(--transition);
}

.hero-interactive-icon i {
  font-size: 2rem;
  margin-bottom: var(--spacing-xs);
}

.hero-interactive-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* About Section */
.about-section {
  padding: var(--spacing-xl) 0;
}

.split-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  align-items: center;
}

.about-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--border-radius);
}

.about-text {
  font-size: 1.1rem;
  line-height: 1.8;
}

/* Menu Categories */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.menu-card {
  overflow: hidden;
  position: relative;
}

/* Target only the category cards on the home page */
.category-card .card-image-wrapper {
  position: relative;
  overflow: hidden;
  height: 250px;/*Set height here */
}

.category-card .card-image-wrapper img{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

/* Hover effect */
.category-card:hover .card-image-wrapper img {
  transform: scale(1.1);
}

.category-card .card-content {
  padding: var(--spacing-md);
  text-align: center;
}
.category-card .card-title{
  margin-bottom: var(--spacing-sm);
  font-size: 1.5rem;
  color: var(--text-color);
}
.category-card .card-description{
  color: var(--text-light);
  margin-bottom: var(--spacing-sm);
}
/* Style the link that wraps the entire card (optional) */
.card-link {
  text-decoration: none;
  color: inherit;
  display: block;
}


/* Testimonials Section */
.testimonials-section {
  background: var(--background-color);
  padding: var(--spacing-xl) 0;
}

.testimonials-slider {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-lg) 0;
}

.testimonials-wrapper {
  position: relative;
  overflow: hidden;
  height: 300px; /* Fixed height for consistency */
}

.testimonial-card {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.5s ease;
  background: var(--white);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

.testimonial-card.active {
  opacity: 1;
  transform: translateX(0);
}

.testimonial-content {
  text-align: center;
}

.testimonial-rating {
  color: #FFD700;
  margin-bottom: var(--spacing-sm);
  font-size: 1.2rem;
}

.testimonial-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
  font-style: italic;
}

.testimonial-author {
  font-weight: 600;
  color: var(--primary-color);
}

.slider-controls {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.slider-prev,
.slider-next {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.slider-prev:hover,
.slider-next:hover {
  background: var(--primary-color-dark);
}

/* Footer Styles */
.footer {
  position: relative;
  background-color: var(--secondary-color);
  color: var(--white);
  padding: var(--spacing-xl) 0 var(--spacing-md);
  margin-top: auto; /* Push footer to the bottom */
  width: 100%;
}

.footer-content {
  position: relative;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footer-heading {
  color: var(--white);
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-link {
  color: var(--light-gray);
  text-decoration: none;
  transition: color var(--transition);
  display: inline-block;
  padding: var(--spacing-xs) 0;
}

.footer-link:hover {
  color: var(--white);
}

.footer-section p {
  color: var(--light-gray);
  margin: 0;
  line-height: 1.6;
}

.footer-section a {
  color: var(--light-gray);
  text-decoration: none;
  transition: color var(--transition);
}

.footer-section a:hover {
  color: var(--white);
}

.social-links {
  display: flex;
  gap: var(--spacing-md);
}

.social-links a {
  color: var(--white);
  font-size: 1.2rem;
  transition: color var(--transition);
}

.social-links a:hover {
  color: var(--primary-color);
}

.copyright {
  text-align: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--light-gray);
  font-size: 0.9rem;
}
/* Additional Footer Elements */
.footer-section address {
  font-style: normal;
  color: var(--light-text);
  line-height: 1.6;
}

.footer-section a[href^="tel:"],
.footer-section a[href^="mailto:"] {
  color: var(--light-text);
  text-decoration: none;
  transition: color var(--transition);
}

.footer-section a[href^="tel:"]:hover,
.footer-section a[href^="mailto:"]:hover {
  color: var(--primary-color);
}

/* Animation for footer sections */
.footer-section {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.footer-section:nth-child(1) { animation-delay: 0.1s; }
.footer-section:nth-child(2) { animation-delay: 0.2s; }
.footer-section:nth-child(3) { animation-delay: 0.3s; }
.footer-section:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
  from {
      opacity: 0;
      transform: translateY(20px);
  }
  to {
      opacity: 1;
      transform: translateY(0);
  }
}


/* Confirmation Page Styles */
.confirmation-container {
  padding: var(--spacing-xl) 0;
  min-height: calc(100vh - 200px);
  background: linear-gradient(135deg, var(--background-color) 0%, #ffffff 100%);
}

.confirmation-card {
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-xl);
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.success-animation {
  text-align: center;
  margin-bottom: var(--spacing-lg);
  animation: scaleIn 0.5s ease-out;
}

.success-animation i {
  font-size: 4rem;
  color: #2ecc71;
}

.confirmation-title {
  text-align: center;
  color: var(--secondary-color);
  margin-bottom: var(--spacing-xl);
  font-size: 2rem;
  font-weight: 700;
}

.confirmation-number {
  text-align: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-sm);
  background: var(--background-color);
  border-radius: var(--border-radius);
}

.confirmation-number .label {
  color: var(--text-light);
  font-size: 0.9rem;
  margin-bottom: var(--spacing-xs);
}

.confirmation-number .value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.order-summary,
.delivery-info,
.support-info {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.order-summary:hover,
.delivery-info:hover,
.support-info:hover {
  box-shadow: var(--shadow-sm);
}

.order-summary h2,
.delivery-info h2,
.support-info h2 {
  color: var(--secondary-color);
  font-size: 1.25rem;
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.summary-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid var(--border-color);
}

.summary-item.total {
  border-top: 2px solid var(--border-color);
  border-bottom: none;
  margin-top: var(--spacing-sm);
  padding-top: var(--spacing-sm);
  font-weight: 700;
}

.delivery-info .info-content {
  display: grid;
  gap: var(--spacing-md);
}

.address-details {
  line-height: 1.6;
}

.delivery-time {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  font-weight: 500;
}

.contact-methods {
  display: grid;
  gap: var(--spacing-sm);
}

.contact-method {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-color);
  text-decoration: none;
  transition: var(--transition);
}

.contact-method:hover {
  color: var(--primary-color);
}

.actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-top: var(--spacing-xl);
}

/* Animations */
@keyframes scaleIn {
  from {
      transform: scale(0);
      opacity: 0;
  }
  to {
      transform: scale(1);
      opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: var(--mobile)) {
  .confirmation-card {
      padding: var(--spacing-md);
      margin: 0 var(--spacing-sm);
  }

  .actions {
      flex-direction: column;
  }

  .confirmation-title {
      font-size: 1.5rem;
  }
}

/* Hero Carousel Styles */
.hero-carousel {
position: absolute;
top: 0;
left: 0;
width: 100%;
height: 100%;
overflow: hidden;
}

.hero-slides {
  position: relative;
  width: 100%;
  height: 100%;
}

/* .hero-slide - this is defined above*/

.hero-slide.active {
  opacity: 1;
}

.carousel-nav {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.carousel-prev,
.carousel-next {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.carousel-prev:hover,
.carousel-next:hover {
  background: rgba(255, 255, 255, 0.3);
}

.carousel-dots {
  display: flex;
  gap: 0.5rem;
}

.carousel-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: none;
  cursor: pointer;
  padding: 0;
  transition: background-color 0.3s ease;
}

.carousel-dot.active {
  background: var(--white);
}

/* Ensure hero content stays on top */
.hero-content {
  position: relative;
  z-index: 5;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-lg);
  text-decoration: none;
  border-radius: var(--border-radius);
  transition: var(--transition);
  font-weight: 500;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
  border: 2px solid var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-secondary {
  background-color: transparent;
  color: var(--secondary-color);
  border: 2px solid var(--secondary-color);
}

.btn-secondary:hover {
  background-color: var(--secondary-color);
  color: var(--white);
}

/* Add styles for animation */
.btn-animated {
  position: relative;
  overflow: hidden;
}

.btn-animated::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: width 0.4s ease-out, height 0.4s ease-out, transform 0.4s ease-out;
}

.btn-animated:hover::before {
  width: 200px; /* Adjust size as needed */
  height: 200px; /* Adjust size as needed */
  transform: translate(-50%, -50%) scale(1);
}
/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-heading {
      font-size: 2.5rem;
  }

  .hero-subheading {
      font-size: 1.2rem;
  }

  .hero-interactive-list {
      flex-wrap: wrap;
      justify-content: center;
  }
  .split-layout {
      grid-template-columns: 1fr;
  }

}

@media (max-width: 480px) {
  .hero-heading {
      font-size: 2rem;
  }

  .hero-content {
      text-align: center;
  }

  .categories-grid {
      grid-template-columns: 1fr;
  }

}
.section-heading {
  text-align: center;
   margin-top: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}
/* Modal Styles (added here for completeness) */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  overflow: auto; /* Enable scrolling for modal content */
  justify-content: center;
  align-items: center;
}

.modal-content {
  background-color: var(--white);
  margin: 5% auto; /* Adjusted margin */
  padding: var(--spacing-md);
  width: 90%;
  max-width: 1000px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  position: relative; /* For close button */
}

/* Modal Close Button */
.close-modal {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-md);
  font-size: 1.5rem;
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
  color: var(--text-light);
  transition: var(--transition);
}
.close-modal:hover {
  color: var(--text-color)
}
.modal-header {
  display:flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  margin-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--secondary-color);
}

.modal-body {
  padding: var(--spacing-sm) 0;
  overflow-y: auto; /* Add scroll for content overflow */
  max-height: 60vh; /* Limit maximum height */
}
/* Modal Sizes */
.modal.small .modal-content {
  max-width: 400px;
}

.modal.medium .modal-content {
  max-width: 600px;
}

.modal.large .modal-content {
  max-width: 900px;
}

/* Alert Styles (added here for completeness) */
.alert {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 10px 20px;
  border-radius: var(--border-radius);
  color: white;
  z-index: 1001;  /* Above the modal */
  opacity: 0;
  transform: translateY(-30px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.alert.show {
  opacity: 1;
  transform: translateY(0);
}

.alert-success {
  background-color: #5cb85c;
}

.alert-info {
  background-color: #5bc0de;
}

.alert-warning {
  background-color: #f0ad4e;
}

.alert-error {
  background-color: #d9534f;
}

/* Loader Styles */
.loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
}

.loader-inline {
  position: relative;
  min-height: 100px;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loader-small .spinner {
  width: 20px;
  height: 20px;
}

.loader-medium .spinner {
  width: 40px;
  height: 40px;
}

.loader-large .spinner {
  width: 60px;
  height: 60px;
}

.loader-text {
  color: var(--white);
  font-size: 0.9rem;
  text-align: center;
}

/* Button Loading State */
.btn.loading {
  position: relative;
  cursor: not-allowed;
  opacity: 0.8;
}

.btn-loader {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--white);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin-right: var(--spacing-xs);
  vertical-align: middle;
}

/* Loading Skeleton */
.loading-skeleton {
  background: linear-gradient(
    90deg,
    var(--background-color) 25%,
    var(--border-color) 50%,
    var(--background-color) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Content Loading States */
.content-loading {
  position: relative;
  min-height: 200px;
}

.content-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1;
}

.content-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 2;
}

/* Internal pages hero adjustment */
.hero-page {
  margin-top: 65px; /* Match new header height */
}

/* Generic Hero for internal pages (FAQ, Terms, Privacy, Account) */
.page-hero {
  background-color: var(--secondary-color);
  color: var(--white);
  text-align: center;
  padding: calc(var(--spacing-xxl) + var(--header-height)) 0 var(--spacing-xl) 0;
  margin-bottom: var(--spacing-xl);
  position: relative;
  z-index: 1;
}

.page-hero .hero-title {
  font-size: 3rem;
  margin-bottom: var(--spacing-sm);
  margin-top: var(--spacing-xl);
}

.page-hero .hero-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: var(--spacing-lg);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .header {
    height: 60px;
  }

  .hero-page {
    margin-top: 60px;
  }

  .logo img {
    height: 50px;
  }

  :root {
    --header-height: 60px;
  }

  .page-hero {
    padding-top: calc(var(--spacing-xl) + var(--header-height));
  }
  
  .page-hero .hero-title {
    font-size: 2.5rem;
    margin-top: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .header {
    height: 55px;
  }

  .hero-page {
    margin-top: 55px;
  }

  .logo img {
    height: 45px;
  }

  :root {
    --header-height: 55px;
  }

  .page-hero {
    padding-top: calc(var(--spacing-lg) + var(--header-height));
  }
  
  .page-hero .hero-title {
    font-size: 2rem;
    margin-top: var(--spacing-md);
  }
}
