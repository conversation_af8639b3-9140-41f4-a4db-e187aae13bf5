const winston = require('winston');
const { ElasticsearchTransport } = require('winston-elasticsearch');

class Logger {
    constructor() {
        this.logger = winston.createLogger({
            level: process.env.LOG_LEVEL || 'info',
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.json()
            ),
            transports: [
                new winston.transports.File({ 
                    filename: 'logs/error.log', 
                    level: 'error',
                    maxsize: 5242880, // 5MB
                    maxFiles: 5,
                }),
                new winston.transports.File({ 
                    filename: 'logs/combined.log',
                    maxsize: 5242880,
                    maxFiles: 5,
                })
            ]
        });

        if (process.env.NODE_ENV === 'production') {
            this.logger.add(new ElasticsearchTransport({
                level: 'info',
                clientOpts: { node: process.env.ELASTICSEARCH_URL }
            }));
        }

        if (process.env.NODE_ENV !== 'production') {
            this.logger.add(new winston.transports.Console({
                format: winston.format.simple()
            }));
        }
    }

    logSecurityEvent(event) {
        this.logger.warn({
            message: 'Security Event',
            ...event,
            timestamp: new Date().toISOString()
        });
    }

    logError(error, context = {}) {
        this.logger.error({
            message: error.message,
            stack: error.stack,
            ...context,
            timestamp: new Date().toISOString()
        });
    }
}

module.exports = new Logger();