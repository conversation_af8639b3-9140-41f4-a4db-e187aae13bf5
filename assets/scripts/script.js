// script.js - Core Functionality for Magic Menu Website

// Add performance monitoring
const performanceMonitor = {
    timings: {},
    
    start(label) {
        this.timings[label] = performance.now();
    },
    
    end(label) {
        if (this.timings[label]) {
            const duration = performance.now() - this.timings[label];
            console.debug(`${label}: ${duration.toFixed(2)}ms`);
            delete this.timings[label];
        }
    }
};

// Import Modules
import { HomePage } from './home.js';
import { Modal, Alert, FormValidator, CartManager, Loader, Utils, CartSidebar } from './components.js';
import MenuDetail from './menuDetail.js';
import Analytics from './services/Analytics.js';
import ConsentManager from './services/ConsentManager.js';

// Global scope for menuDetail, initialized only on the menu page
let menuDetail; // Declare it here, initialize it conditionally.

document.addEventListener('DOMContentLoaded', () => {
    // Initialize consent manager first
    ConsentManager.init();

    // Example of tracking different events
    if (Analytics.consent) {
        // Track page view with page type
        Analytics.trackPageView(getPageType());

        // Track menu item views
        document.querySelectorAll('.menu-item').forEach(item => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        Analytics.trackEcommerce('view_item', {
                            id: item.dataset.itemId,
                            name: item.dataset.itemName,
                            price: parseFloat(item.dataset.price),
                            category: item.dataset.category
                        });
                    }
                });
            }, { threshold: 0.5 });

            observer.observe(item);
        });
    }

    // Track cart actions
    document.addEventListener('cartUpdated', (e) => {
        if (!Analytics.consent) return;

        switch (e.detail?.action) {
            case 'add':
                Analytics.trackEcommerce('add_to_cart', e.detail.item);
                break;
            case 'remove':
                Analytics.track('remove_from_cart', {
                    item_id: e.detail.itemId
                });
                break;
            case 'update':
                Analytics.track('update_cart_quantity', {
                    item_id: e.detail.itemId,
                    quantity: e.detail.quantity
                });
                break;
        }
    });

    // Track checkout steps
    if (document.querySelector('.checkout-page')) {
        const cart = CartManager.getCartItems();
        const totals = CartManager.getTotals();

        Analytics.trackEcommerce('begin_checkout', {
            items: cart,
            value: totals.total,
            shipping: totals.shipping,
            tax: totals.tax
        });
    }

    performanceMonitor.start('initialization');
    // Initialize home page if we're on the home page
    if (document.querySelector('.hero')) {
        new HomePage();
    }

    // Initialize animations
    initializeAnimations();

    // Initialize scroll effects
    initializeScrollEffects();

    // Initialize smooth scrolling
    initializeSmoothScroll();

    // Initialize cart sidebar only if the elements exist
    const cartSidebarElement = document.querySelector('.cart-sidebar');
    if (cartSidebarElement) {
        new CartSidebar();
    }

    // --- Global Functionality ---
    // Lazy Loading for Images
    initializeLazyLoading();

    // Update Cart Icon Count
    updateCartIcon();
      // Handle Mobile Navigation Toggle
      const header = document.querySelector('.header');
      const navList = document.querySelector('.nav-list');
      header.addEventListener('click', (e) => {
        if (e.target.classList.contains('logo') || e.target.classList.contains('cart-icon')) {
            // Ensure navigation isn't closed by clicking logo or cart-icon
        }
        else if (header.classList.contains('nav-open')) {
          header.classList.remove('nav-open');
          navList.style.maxHeight = null;
        }
        else if (e.target.classList.contains('nav-item') || e.target.classList.contains('nav-link')) {
            header.classList.remove('nav-open');
            navList.style.maxHeight = null;
        } else if (e.target.classList.contains('main-nav')) {
        header.classList.toggle('nav-open');
        navList.style.maxHeight = header.classList.contains('nav-open') ? navList.scrollHeight + 'px' : null;
        }

    });



    // --- Home Page (`index.html`) Functionality ---
    if (document.querySelector('.testimonials-slider')) {
      initTestimonialSlider();
    }

    // --- Menu Page (`menu.html`) Functionality ---
    if (document.querySelector('.menu-page')) {
        const menuDetail = new MenuDetail(); // Initialize here ONLY on the menu page

        // Event Delegation for Quick View buttons. Attach to a parent element.
        document.querySelector('.menu-content').addEventListener('click', (e) => {
            const quickViewBtn = e.target.closest('.quick-view-btn');
            if (quickViewBtn) {
                e.preventDefault(); // Prevent any default behavior
                
                const card = quickViewBtn.closest('.menu-item-card');
                if (!card) {
                    console.error('Menu item card not found');
                    return;
                }

                console.log('Card data:', card.dataset); // Debug log
                
                const itemData = {
                    id: card.dataset.id || crypto.randomUUID(),
                    image: card.querySelector('img').src,
                    title: card.dataset.title,
                    description: card.dataset.description,
                    price: card.dataset.price,
                    nutrition: {
                        calories: card.dataset.calories || 'N/A',
                        protein: card.dataset.protein || 'N/A',
                        carbs: card.dataset.carbs || 'N/A',
                    },
                    ingredients: card.dataset.ingredients ? card.dataset.ingredients.split(', ') : ['N/A'],
                };

                console.log('Opening quick view with data:', itemData); // Debug log
                menuDetail.openModal(itemData);
            }
        });
    }


    // --- Cart Page (`cart.html`) Functionality ---
     if (document.querySelector('.cart-page')) {
        initCartPage();
    }


    // --- Checkout Page (`checkout.html`) Functionality ---
     if (document.querySelector('.checkout-page')) {
        initCheckoutPage();
    }


     // --- Account Page (`account.html`) Functionality ---
     if (document.querySelector('.account-page')) {
        initAccountPage();
    }

    // --- Contact Page (`contact.html`) Functionality ---
    if (document.querySelector('.contact-page')) {
        initContactPage();
    }


    // --- FAQ Page (`faq.html`) Functionality ---
    if (document.querySelector('.legal-page')) {
      initFaqPage();
    }
    performanceMonitor.end('initialization');
});

// ----- Helper functions -----

// Update cart Icon
export function updateCartIcon() {
    const cartItems = CartManager.getCartItems();
    const totalItems = cartItems.reduce((sum, item) => sum + (item.quantity || 1), 0);
    CartManager.updateCartCount(totalItems);
}

// --- Menu Page (`menu.html`) specific functionality
function initMenuPage() {
    const categoryNav = document.querySelector('.category-nav');
    const categoryLinks = document.querySelectorAll('.category-link');
    const menuSections = document.querySelectorAll('.menu-section');
    const menuCategories = document.querySelectorAll('.menu-category');

    // Function to update active link
    function updateActiveLink(sectionId) {
        categoryLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${sectionId}`) {
                link.classList.add('active');
                // Also activate parent if it's a sub-category
                const parentLi = link.closest('.category-item');
                 if(parentLi){
                  const parentLink = parentLi.querySelector(':scope > .category-link') //direct child
                    if(parentLink) {
                        parentLink.classList.add('active');
                    }
                 }
            }
        });
    }


     // Intersection Observer for scroll detection - observe both sections and categories
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting ) {
               // && entry.intersectionRatio >= 0.5 - removed this to make the snapping more responsive
                // console.log("Intersecting:", entry.target.id, entry.intersectionRatio); // Debugging
                updateActiveLink(entry.target.id);
            }
        });
    }, {
        threshold: [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0], //observe multiple thresholds
        rootMargin: '-50px 0px -40% 0px'  // Adjusted for better responsiveness.
    });


    // Observe all menu sections *and* categories
    menuSections.forEach(section => {
        observer.observe(section);
    });
    menuCategories.forEach(category => {
        observer.observe(category);
    });


    // Handle click events on category links
    categoryNav.addEventListener('click', (event) => {
        const link = event.target.closest('.category-link');
        if (!link) return;

        event.preventDefault();

        // Get the target section id
        const targetId = link.getAttribute('href').substring(1);
        const targetSection = document.getElementById(targetId);

        if (targetSection) {
            // Update active state
            updateActiveLink(targetId);

            // Scroll to section
            targetSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });

      // Set initial active state based on current scroll position
    function setInitialActiveSection() {
      const sections = Array.from(menuSections);
      const categories = Array.from(menuCategories);
      const allElements = sections.concat(categories);

      for (const element of allElements) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= window.innerHeight / 2 && rect.bottom >= window.innerHeight / 2) {
              updateActiveLink(element.id);
              break; // Stop after finding the first visible section/category.
          }
        }
    }

    // Set initial active state on page load
    setInitialActiveSection();
}

// --- Cart Page (`cart.html`) specific functionality
function initCartPage() {
  const cartItemsList = document.querySelector('.cart-items-list');
  const cartSummary = document.querySelector('.cart-summary');
  const subtotalElement = cartSummary.querySelector('.subtotal');
  const taxElement = cartSummary.querySelector('.tax');
  const deliveryElement = cartSummary.querySelector('.delivery');
  const totalElement = cartSummary.querySelector('.total-value');
    const emptyCartMessage = document.querySelector('.empty-cart-message');
    const cartItems = CartManager.getCartItems();

   // Function to update the cart display
    function updateCartDisplay() {
        let subtotal = 0;
        cartItemsList.innerHTML = '';
        if (cartItems.length === 0) {
          cartItemsList.style.display = 'none';
          emptyCartMessage.style.display = 'block';
          cartSummary.style.display = 'none';
          return;
        } else {
         cartItemsList.style.display = 'block';
          emptyCartMessage.style.display = 'none';
          cartSummary.style.display = 'block';
        }


        cartItems.forEach((item, index) => {
            const cartItemDiv = document.createElement('div');
            cartItemDiv.classList.add('cart-item', 'card');
            cartItemDiv.innerHTML = `
              <img src="images/pizza1.jpg" alt="${item.name}" class="cart-item-image" loading="lazy">
              <div class="cart-item-details">
                  <h3 class="cart-item-title">${item.name}</h3>
                   <p class="cart-item-price">${Utils.formatCurrency(item.price)}</p>
                   <div class="cart-item-quantity">
                       <label for="quantity${index}">Quantity:</label>
                           <div class="quantity-controls">
                            <button class="quantity-btn decrease-btn" aria-label="Decrease quantity" data-id="${item.id}">-</button>
                             <input type="number" id="quantity${index}" class="quantity-input" value="${item.quantity}" min="1" data-id="${item.id}">
                             <button class="quantity-btn increase-btn" aria-label="Increase quantity" data-id="${item.id}">+</button>
                           </div>
                      </div>
                </div>
                 <button class="btn btn-secondary remove-item-btn" aria-label="Remove item" data-id="${item.id}">Remove</button>
            `;
              cartItemsList.appendChild(cartItemDiv);
             subtotal += item.price * item.quantity;
        });

     updateSummary(subtotal);

    }

     function updateSummary(subtotal) {
          const deliveryFee = 5;
         const taxRate = 0.05;
          const tax = subtotal * taxRate;
          const total = subtotal + tax + deliveryFee;
          subtotalElement.textContent = Utils.formatCurrency(subtotal);
          taxElement.textContent = Utils.formatCurrency(tax);
          deliveryElement.textContent = Utils.formatCurrency(deliveryFee);
          totalElement.textContent = Utils.formatCurrency(total);

     }

      cartItemsList.addEventListener('click', (event) => {
        if (event.target.classList.contains('decrease-btn')) {
            const itemId = event.target.dataset.id;
          updateItemQuantity(itemId, -1);
        }
         if (event.target.classList.contains('increase-btn')) {
              const itemId = event.target.dataset.id;
            updateItemQuantity(itemId, 1);
         }
        if (event.target.classList.contains('remove-item-btn')) {
              const itemId = event.target.dataset.id;
             removeItemFromCart(itemId);
         }
       });


      function updateItemQuantity(itemId, change) {
          const updatedCart = CartManager.getCartItems().map(item => {
            if (item.id === itemId && (item.quantity > 1 || change > 0)) {
                  return { ...item, quantity: item.quantity + change };
            }
            return item;
          }).filter(item => item.quantity > 0);

          CartManager.saveCartItems(updatedCart);
           updateCartDisplay();
          updateCartIcon();
        }

       function removeItemFromCart(itemId) {
             const updatedCart = CartManager.getCartItems().filter(item => item.id !== itemId);
             CartManager.saveCartItems(updatedCart);
           updateCartDisplay();
            updateCartIcon();
         }
        updateCartDisplay();
}

// --- Checkout Page (`checkout.html`) Specific Functionality
function initCheckoutPage() {
     const deliveryDetails = document.querySelector('.delivery-details');
     const pickupDetails = document.querySelector('.pickup-details');
     const deliveryOptions = document.querySelectorAll('input[name="delivery-option"]');
      const checkoutForm = document.querySelector('.checkout-form');


      // Event listener to switch between delivery options
      deliveryOptions.forEach(option => {
          option.addEventListener('change', (e) => {
              if (e.target.value === 'delivery') {
                  deliveryDetails.style.display = 'block';
                  pickupDetails.style.display = 'none';
              } else {
                 deliveryDetails.style.display = 'none';
                 pickupDetails.style.display = 'block';
              }
          });
      });

      // Form Submit Event
      checkoutForm.addEventListener('submit', (e) => {
        e.preventDefault(); // Prevent page reload

        if (!FormValidator.validateRequiredFields(checkoutForm)) {
         Alert.show("Please fill out all required fields correctly.", 'error');
         return;
        }

       if (document.querySelector('input[name="delivery-option"]:checked').value === 'delivery') {
            if (!FormValidator.validatePhone(document.getElementById('phone').value)) {
              Alert.show("Invalid phone number. Please use a valid format.", 'error');
              return;
             }
        }
        else {
          if (!FormValidator.validatePhone(document.getElementById('pickup-phone').value)) {
            Alert.show("Invalid phone number. Please use a valid format.", 'error');
              return;
          }
        }

          Alert.show("Your order has been placed! Redirecting to Order Confirmation.", 'success');
          Loader.show();
         setTimeout(() => {
               Loader.hide();
              window.location.href = 'confirmation.html'; //Placeholder to confirmation page
        }, 2000);
      });
}

// --- Account Page (`account.html`) Specific Functionality
function initAccountPage() {
  const loginForm = document.querySelector('.login-form');
   const signupForm = document.querySelector('.signup-form');

    // Handle form submission for login form
    if(loginForm) {
    loginForm.addEventListener('submit', (e) => {
        e.preventDefault(); // Prevent page reload
        if (!FormValidator.validateRequiredFields(loginForm)) {
         Alert.show("Please fill out all required fields correctly.", 'error');
         return;
        }
         Alert.show("You have successfully logged in.", 'success');
      // Perform login logic here
      });
    }
    // Handle form submission for signup form
    if(signupForm) {
      signupForm.addEventListener('submit', (e) => {
        e.preventDefault();  //Prevent page reload
          if (!FormValidator.validateRequiredFields(signupForm)) {
              Alert.show("Please fill out all required fields correctly.", 'error');
              return;
           }
          if (!FormValidator.validateEmail(document.getElementById('signup-email').value)) {
               Alert.show("Invalid Email format, please provide a valid email.", 'error');
             return;
          }
         Alert.show("You have successfully registered.", 'success');
       // Perform signup logic here
      });
    }

}

// --- Contact Page (`contact.html`) Specific Functionality
function initContactPage() {
    const contactForm = document.querySelector('.contact-form');

    contactForm.addEventListener('submit', (e) => {
        e.preventDefault(); // Prevent page reload

        if (!FormValidator.validateRequiredFields(contactForm)) {
             Alert.show("Please fill out all required fields correctly.", 'error');
            return;
        }
        if (!FormValidator.validateEmail(document.getElementById('email').value)) {
           Alert.show("Invalid Email format, please provide a valid email.", 'error');
           return;
         }
        Alert.show("Your message has been sent!", 'success');

        // Handle submission of contact form here
    });
}

// --- FAQ Page (`faq.html`) Specific Functionality
function initFaqPage() {
    const faqItems = document.querySelectorAll('.faq-item');
      faqItems.forEach(item => {
        const question = item.querySelector('.faq-question button');
        const answer = item.querySelector('.faq-answer');
          question.addEventListener('click', () => {
            answer.classList.toggle('open');
             question.querySelector('i').classList.toggle('fa-plus');
               question.querySelector('i').classList.toggle('fa-minus');
          });
      });
}

// ----- Testimonial Slider Implementation for Home page-----

function initTestimonialSlider() {
    const slider = document.querySelector('.testimonials-slider');
    if (!slider) return;

    const testimonials = document.querySelectorAll('.testimonial-card');
    const prevBtn = slider.querySelector('.slider-prev');
    const nextBtn = slider.querySelector('.slider-next');
    let currentIndex = 0;

    function showTestimonial(index) {
        testimonials.forEach((testimonial, i) => {
            testimonial.classList.remove('active');
            if (i === index) {
                testimonial.classList.add('active');
            }
        });
    }

    function nextTestimonial() {
        currentIndex = (currentIndex + 1) % testimonials.length;
        showTestimonial(currentIndex);
    }

    function prevTestimonial() {
        currentIndex = (currentIndex - 1 + testimonials.length) % testimonials.length;
        showTestimonial(currentIndex);
    }

    // Event listeners
    if (prevBtn) prevBtn.addEventListener('click', prevTestimonial);
    if (nextBtn) nextBtn.addEventListener('click', nextTestimonial);

    // Auto-advance every 5 seconds
    const autoAdvance = setInterval(nextTestimonial, 5000);

    // Stop auto-advance on hover
    slider.addEventListener('mouseenter', () => clearInterval(autoAdvance));
    slider.addEventListener('mouseleave', () => setInterval(nextTestimonial, 5000));

    // Show first testimonial
    showTestimonial(currentIndex);
}

document.addEventListener('DOMContentLoaded', () => {
 // Add 'loaded' class to the body once all resources are loaded for smooth transitions.
  document.body.classList.add('loaded');
});

function initializeAnimations() {
    // Add animation classes to elements
    const elements = document.querySelectorAll('.section');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, {
        threshold: 0.1
    });
    
    elements.forEach(element => {
        observer.observe(element);
    });
}

function initializeScrollEffects() {
    let lastScroll = 0;
    const header = document.querySelector('.header');
    
    window.addEventListener('scroll', () => {
        const currentScroll = window.pageYOffset;
        
        // Header show/hide effect
        if (currentScroll > lastScroll && currentScroll > 100) {
            header.style.transform = 'translateY(-100%)';
        } else {
            header.style.transform = 'translateY(0)';
        }
        
        // Header background opacity
        if (currentScroll > 50) {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.8)';
        }
        
        lastScroll = currentScroll;
    });
}

function initializeSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

function initializeLazyLoading() {
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.add('loaded');
                observer.unobserve(img);
            }
        });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });
}

function initializeForms() {
    document.querySelectorAll('form').forEach(form => {
        // Add real-time validation
        form.querySelectorAll('input, select, textarea').forEach(field => {
            // Validate on blur
            field.addEventListener('blur', () => {
                FormValidator.validateField(field);
            });

            // Validate on input with debounce
            let debounceTimer;
            field.addEventListener('input', () => {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    FormValidator.validateField(field);
                }, 500);
            });
        });

        // Form submission handling
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            
            // Clear all previous errors
            form.querySelectorAll('.form-group, .input-group').forEach(group => {
                FormValidator.clearError(group);
            });

            // Validate all fields
            const isValid = FormValidator.validateRequiredFields(form);

            if (isValid) {
                // Handle form submission
                const submitBtn = form.querySelector('[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<span class="spinner"></span> Submitting...';
                }

                // Submit form data
                submitForm(form).finally(() => {
                    if (submitBtn) {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = 'Submit';
                    }
                });
            } else {
                // Scroll to first error
                const firstError = form.querySelector('.has-error');
                if (firstError) {
                    firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                
                Alert.show('Please correct the errors in the form', 'error');
            }
        });
    });
}

async function submitForm(form) {
    try {
        const formData = new FormData(form);
        const response = await api.post(form.action, Object.fromEntries(formData));
        
        Alert.show('Form submitted successfully!', 'success');
        form.reset();
        
        // Clear success states
        form.querySelectorAll('.form-group, .input-group').forEach(group => {
            FormValidator.clearError(group);
        });
        
        return response;
    } catch (error) {
        Alert.show(ApiErrorHandler.handleError(error, 'form_submission'), 'error');
        throw error;
    }
}

// Helper function to determine page type
function getPageType() {
    const pageTypes = {
        '.hero': 'home',
        '.menu-page': 'menu',
        '.cart-page': 'cart',
        '.checkout-page': 'checkout',
        '.contact-page': 'contact',
        '.account-page': 'account',
        '.faq-page': 'faq'
    };

    for (const [selector, type] of Object.entries(pageTypes)) {
        if (document.querySelector(selector)) return type;
    }
    return 'other';
}
