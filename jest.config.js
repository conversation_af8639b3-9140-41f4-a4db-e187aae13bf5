module.exports = {
    testEnvironment: 'jsdom',
    setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
    moduleNameMapper: {
        '\\.(css|less|scss)$': '<rootDir>/tests/mocks/styleMock.js',
        '\\.(jpg|jpeg|png|gif|svg)$': '<rootDir>/tests/mocks/fileMock.js'
    },
    coverageDirectory: 'coverage',
    collectCoverageFrom: [
        'assets/scripts/**/*.js',
        '!assets/scripts/vendor/**',
        '!**/node_modules/**'
    ],
    testPathIgnorePatterns: ['/node_modules/', '/vendor/'],
    transform: {
        '^.+\\.js$': 'babel-jest'
    }
};