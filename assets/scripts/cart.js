// cart.js - Manages the shopping cart functionality

import { CartManager } from './components.js';
import { api } from './api.js';

document.addEventListener('DOMContentLoaded', async () => {
    // Initialize AOS
    AOS.init({
        duration: 800,
        once: true,
        offset: 100
    });

    const cartManager = new CartManager();

    function updateCartDisplay() {
        const cartItems = CartManager.getCartItems();
        const cartItemsList = document.querySelector('.cart-items-list');
        const emptyCartMessage = document.querySelector('.empty-cart-message');
        const cartSummary = document.querySelector('.cart-summary');
        
        if (cartItems.length === 0) {
            cartItemsList.style.display = 'none';
            emptyCartMessage.style.display = 'block';
            cartSummary.style.display = 'none';
            return;
        }
        
        cartItemsList.style.display = 'block';
        emptyCartMessage.style.display = 'none';
        cartSummary.style.display = 'block';
        
        // Update cart count using CartManager directly
        const totalItems = cartItems.reduce((sum, item) => sum + (item.quantity || 1), 0);
        CartManager.updateCartCount(totalItems);
    }

    // Initialize cart
    try {
        const cartData = await api.getCart();
        cartManager.updateCart(cartData);
        updateCartDisplay();
    } catch (error) {
        console.error('Failed to load cart:', error);
    }

    // Handle quantity changes
    document.querySelectorAll('.quantity-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const itemId = e.target.closest('.cart-item').dataset.itemId;
            const isIncrease = btn.classList.contains('increase');
            const input = btn.parentElement.querySelector('.quantity-input');
            const newQuantity = parseInt(input.value) + (isIncrease ? 1 : -1);
            
            if (newQuantity > 0) {
                cartManager.updateQuantity(itemId, newQuantity);
                updateCartDisplay();
            }
        });
    });

    // Handle remove item
    document.querySelectorAll('.remove-item-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const itemId = e.target.closest('.cart-item').dataset.itemId;
            cartManager.removeItem(itemId);
            updateCartDisplay();
        });
    });
});

// Initialize cart page if we're on it
if (document.querySelector('.cart-page')) {
    initCartPage();
}

