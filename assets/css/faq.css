/* FAQ Page Styles */
.faq-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    padding: calc(var(--header-height) + var(--spacing-xl)) 0 var(--spacing-xl);
    color: var(--white);
    /* Removed margin-bottom since we're handling spacing in the container */
}

.page-title {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.search-container {
    max-width: 600px;
    margin: 0 auto;
    position: relative;
}

.search-container input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: 50px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: var(--white);
    font-size: 1.1rem;
    transition: var(--transition);
}

.search-container input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-container i {
    position: absolute;
    right: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
}

/* FAQ Layout */
.faq-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg) 0;
}

/* Sidebar */
.faq-sidebar {
    position: sticky;
    top: var(--spacing-xl);
}

.category-nav {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.category-btn {
    padding: var(--spacing-md);
    text-align: left;
    background: none;
    border: none;
    border-radius: var(--border-radius);
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
}

.category-btn:hover,
.category-btn.active {
    background-color: var(--primary-color);
    color: var(--white);
}

/* FAQ Content */
.faq-section {
    margin-bottom: var(--spacing-xl);
}

.section-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    color: var(--secondary-color);
}

.section-title i {
    font-size: 1.5rem;
}

.faq-grid {
    display: grid;
    gap: var(--spacing-md);
}

/* FAQ Items */
.faq-item {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.faq-item:hover {
    box-shadow: var(--shadow-md);
}

.faq-toggle {
    width: 100%;
    padding: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 500;
}

.icon-wrapper {
    position: relative;
    width: 24px;
    height: 24px;
}

.icon-wrapper i {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: var(--transition);
}

.icon-wrapper .fa-minus {
    opacity: 0;
}

.faq-item.active .icon-wrapper .fa-plus {
    opacity: 0;
}

.faq-item.active .icon-wrapper .fa-minus {
    opacity: 1;
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.faq-item.active .faq-answer {
    max-height: 500px;
}

.answer-content {
    padding: 0 var(--spacing-lg) var(--spacing-lg);
    color: var(--text-light);
}

/* Still Have Questions Section */
.still-have-questions {
    text-align: center;
    padding: var(--spacing-xl) 0;
    margin-top: var(--spacing-xl);
    border-top: 1px solid var(--border-color);
}

.contact-btn {
    display: inline-block;
    margin-top: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-xl);
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: var(--transition);
}

.contact-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

/* Animations */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeIn 0.6s ease-out forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 968px) {
    .faq-container {
        grid-template-columns: 1fr;
    }

    .faq-sidebar {
        position: static;
        margin-bottom: var(--spacing-lg);
    }

    .category-nav {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .category-btn {
        flex: 1;
        min-width: 150px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 2rem;
    }

    .faq-toggle {
        padding: var(--spacing-md);
        font-size: 1rem;
    }
}
