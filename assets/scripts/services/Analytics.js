// Analytics service with privacy-first approach and performance optimization
class Analytics {
    constructor() {
        this.initialized = false;
        this.queue = [];
        this.consent = this.getConsentStatus();
        this.debugMode = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    }

    init() {
        if (this.initialized) return;

        // Get measurement ID from meta tag
        const measurementId = document.querySelector('meta[name="ga-measurement-id"]')?.content;
        
        if (!measurementId) {
            console.warn('Analytics: Missing measurement ID');
            return;
        }

        // Initialize GA4
        const script = document.createElement('script');
        script.async = true;
        script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
        document.head.appendChild(script);

        window.dataLayer = window.dataLayer || [];
        window.gtag = function() { dataLayer.push(arguments); }
        gtag('js', new Date());
        gtag('config', measurementId, {
            send_page_view: false, // We'll handle this manually
            anonymize_ip: true,
            cookie_flags: 'SameSite=Strict;Secure',
            cookie_domain: window.location.hostname
        });

        this.initialized = true;
        this.processQueue();
        this.initializePerformanceTracking();
    }

    track(eventName, params = {}) {
        if (!this.consent || !eventName) return;

        const event = {
            event_name: eventName,
            ...params,
            timestamp: new Date().toISOString(),
            page_url: window.location.href,
            page_title: document.title
        };

        if (!this.initialized) {
            this.queue.push(event);
            return;
        }

        if (this.debugMode) {
            console.debug('Analytics Event:', event);
        }

        gtag('event', eventName, params);
    }

    trackPageView(pageType = '') {
        const loadTime = window.performance?.timing?.loadEventEnd - 
                        window.performance?.timing?.navigationStart;

        this.track('page_view', {
            page_type: pageType,
            page_load_time: loadTime || 0,
            referrer: document.referrer
        });
    }

    trackEcommerce(action, data) {
        switch (action) {
            case 'view_item':
                this.track('view_item', {
                    items: [this.formatProductData(data)]
                });
                break;

            case 'add_to_cart':
                this.track('add_to_cart', {
                    items: [this.formatProductData(data)]
                });
                break;

            case 'begin_checkout':
                this.track('begin_checkout', {
                    items: data.items.map(this.formatProductData),
                    value: data.value,
                    currency: 'USD'
                });
                break;

            case 'purchase':
                this.track('purchase', {
                    transaction_id: data.orderId,
                    value: data.value,
                    tax: data.tax,
                    shipping: data.shipping,
                    currency: 'USD',
                    items: data.items.map(this.formatProductData)
                });
                break;
        }
    }

    formatProductData(product) {
        return {
            item_id: product.id,
            item_name: product.name,
            price: product.price,
            quantity: product.quantity || 1,
            item_category: product.category
        };
    }

    initializePerformanceTracking() {
        // Track Core Web Vitals
        if ('web-vital' in window) {
            webVitals.getCLS(metric => this.trackWebVital('CLS', metric));
            webVitals.getFID(metric => this.trackWebVital('FID', metric));
            webVitals.getLCP(metric => this.trackWebVital('LCP', metric));
        }

        // Track custom performance metrics
        this.trackPerformanceMetrics();
    }

    trackWebVital(name, metric) {
        this.track('web_vital', {
            metric_name: name,
            metric_value: metric.value,
            metric_rating: metric.rating
        });
    }

    trackPerformanceMetrics() {
        if (!window.performance) return;

        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = window.performance.timing;
                const pageLoadTime = perfData.loadEventEnd - perfData.navigationStart;
                const domLoadTime = perfData.domContentLoadedEventEnd - perfData.navigationStart;

                this.track('performance_metrics', {
                    page_load_time: pageLoadTime,
                    dom_load_time: domLoadTime,
                    server_response_time: perfData.responseEnd - perfData.requestStart
                });
            }, 0);
        });
    }

    getConsentStatus() {
        return localStorage.getItem('analytics_consent') === 'true';
    }

    setConsent(granted) {
        localStorage.setItem('analytics_consent', granted);
        this.consent = granted;

        if (granted && !this.initialized) {
            this.init();
        }
    }

    processQueue() {
        while (this.queue.length) {
            const event = this.queue.shift();
            this.track(event.event_name, event);
        }
    }
}

export default Analytics;
