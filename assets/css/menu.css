/* Menu Page Specific Styles */
.menu-hero {
    background-color: var(--secondary-color);
    color: var(--white);
    text-align: center;
    padding: calc(var(--spacing-xxl) + var(--header-height)) 0 var(--spacing-xl) 0;
    margin-bottom: var(--spacing-xl);
    position: relative;
    z-index: 1;
}
/* Ensure content is visible */
.menu-container {
    position: relative;
    z-index: 2;
}
.menu-content {
    position: relative;
    z-index: 2;
}
/* Make sure menu items are visible */
.menu-item-card {
    background-color: var(--white);
    position: relative;
    z-index: 2;
}

.menu-title {
    font-size: 3rem;
    margin-bottom: var(--spacing-sm);
    margin-top: var(--spacing-xl); /* Added margin-top */
}

.menu-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: var(--spacing-lg);
}

.menu-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-lg) 0;
}

/* Sidebar Navigation */
.menu-sidebar {
    position: sticky;
    top: 100px;
    height: fit-content;
}

.category-nav {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-md);
}

.category-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-item {
    margin-bottom: var(--spacing-sm);
}
/* Top Level Nav Links (Local/International) */
.category-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm);
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
    border-radius: var(--border-radius);
    position: relative;
}

.category-link i {
    margin-right: var(--spacing-sm);
    width: 20px;
    text-align: center;
}

.category-link:hover,
.category-link.active {
    background-color: var(--primary-color);
    color: var(--white);
}
.category-link.active::after { /*Underline for the main active link*/
    content: '';
    position: absolute;
    left:0;
    bottom: -2px;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
    transition: transform 0.3s ease;
}
/* Sub-Category Links (Soups, Stews, etc.) */
.sub-category-list {
    list-style: none;
    padding-left: var(--spacing-md); /* Indent sub-categories */
    margin-top: var(--spacing-xs);
    display: none; /* Hide by default */
}
 .category-item:hover .sub-category-list{
    display: block;  /* Show when parent is hovered */
 }

.sub-category-link {
    padding: var(--spacing-xs);
    font-size: 0.9rem; /* Slightly smaller font */
    border-radius: calc(var(--border-radius)/2); /*Slightly smaller radius*/
}

/* Style for the active sub-category */
.sub-category-link.active {
    background-color: var(--background-light); /* Lighter background */
    color: var(--primary-color);
    font-weight: 600;
}
.sub-category-link.active:hover,
.sub-category-link.active:focus {
    background-color: var(--background-light); /*override parent hover effect*/
}



/* Menu Content */
.menu-content {
    flex: 1;
}

.menu-section {
    margin-bottom: var(--spacing-xl);
}

.section-heading {
    font-size: 2rem;
    margin-bottom: var(--spacing-lg);
    color: var(--text-color);
}

/* Menu Category Headings (e.g., "Soups", "Stews") */
.menu-category {
    margin-bottom: var(--spacing-md);
}

.category-heading {
    font-size: 1.5rem;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
    padding-bottom: var(--spacing-xs);
}

/* Menu Grid */
.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

/* Menu Item Card */
.menu-item-card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: var(--transition);
}

.menu-item-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-image-wrapper {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.card-image-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.menu-item-card:hover .card-overlay {
    opacity: 1;
}

.quick-view-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--white);
    color: var(--text-color);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.quick-view-btn:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.card-content {
    padding: var(--spacing-md);
}

.card-title {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-xs);
    color: var(--text-color);
}

.card-description {
    color: var(--text-light);
    margin-bottom: var(--spacing-sm);
    font-size: 0.9rem;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-sm);
}

.card-price {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.add-to-cart-btn {
    width: auto;  /* Changed from 100% to auto */
    padding: var(--spacing-sm) var(--spacing-lg);  /* Reduced padding */
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;  /* Added to better handle icon and text */
    align-items: center;
    gap: var(--spacing-sm);
}

.add-to-cart-btn:hover {
    background-color: var(--primary-dark);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
}

.modal-content {
    position: relative;
    background-color: var(--white);
    margin: 5% auto;
    padding: 20px;
    width: 90%;
    max-width: 1000px;
    border-radius: 8px;
    animation: modalFade 0.3s ease-in-out;
}

.close-modal {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 28px;
    cursor: pointer;
    z-index: 1;
}

.item-detail-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.item-detail-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 8px;
}

.item-detail-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.item-detail-meta {
    display: grid;
    gap: 20px;
}

.nutrition-info ul {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.item-detail-footer {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: auto;
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: none;
    background: var(--primary-color);
    color: white;
    border-radius: 4px;
    cursor: pointer;
}

#modalQuantity {
    width: 50px;
    text-align: center;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

@keyframes modalFade {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Quick View Modal Styles */
.quick-view-modal {
    padding: var(--spacing-lg);
}

.quick-view-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.quick-view-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.quick-view-details {
    display: flex;
    flex-direction: column;
}

.quick-view-title {
    font-size: 1.8rem;
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
}

.quick-view-description {
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.quick-view-price {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

/* Quantity Controls in Quick View */
.quantity-controls-wrapper {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.quantity-label {
    font-weight: 500;
    color: var(--text-color);
}

.quantity-controls {
    display: flex;
    align-items: center;
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    padding: 4px;
}

.quantity-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
}

.quantity-btn:hover {
    color: var(--primary-color);
}

.quantity-input {
    width: 40px;
    text-align: center;
    border: none;
    background: none;
    font-weight: 500;
    color: var(--text-color);
    appearance: textfield;
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
}

.quantity-input::-webkit-outer-spin-button,
.quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Add to Cart Button */
.add-to-cart-btn {
    width: 50%;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    margin-top: auto;
}

.add-to-cart-btn:hover {
    background-color: var(--primary-dark);
}

/* Nutrition Info */
.nutrition-info {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.nutrition-title {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
}

.nutrition-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.nutrition-item {
    text-align: center;
    padding: var(--spacing-sm);
    background-color: var(--background-color);
    border-radius: var(--border-radius);
}

.nutrition-label {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: var(--spacing-xs);
}

.nutrition-value {
    font-weight: 600;
    color: var(--text-color);
}

/* Product Variations Styling */
.variations-container {
    margin: var(--spacing-md) 0;
}

.variation-group {
    margin-bottom: var(--spacing-md);
}

.variation-label {
    display: block;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-color);
}

.variation-select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--white);
    color: var(--text-color);
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-sm);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
}

.variation-checkbox {
    accent-color: var(--primary-color);
}

/* Style for required fields */
.variation-select[required] {
    border-left: 3px solid var(--primary-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .menu-container {
        grid-template-columns: 200px 1fr;
    }
}

@media (max-width: 768px) {
    .menu-container {
        grid-template-columns: 1fr;
    }

    .menu-sidebar {
        position: static;
        margin-bottom: var(--spacing-lg);
    }

    .category-nav {
        overflow-x: auto;
        padding: var(--spacing-sm);
    }

    .category-list {
        display: flex;
        gap: var(--spacing-sm);
        flex-wrap: nowrap; /* Prevent wrapping to keep horizontal scroll */
    }
   .category-item {
        margin-bottom: 0;
    }

    .sub-category-list{
      display: flex;
      gap: var(--spacing-sm);
       margin-left: var(--spacing-md);
    }
  .sub-category-list li {
    margin-right: 0;
   }
    .category-link {
        white-space: nowrap;
    }

    .menu-title {
        font-size: 2.5rem;
    }

    .item-detail-container {
        grid-template-columns: 1fr;
    }

    .item-detail-image img {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .menu-grid {
        grid-template-columns: 1fr;
    }

    .menu-title {
        font-size: 2rem;
    }

    .menu-subtitle {
        font-size: 1rem;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .menu-hero {
        padding-top: calc(var(--spacing-xl) + 60px);
    }
    
    .menu-title {
        font-size: 2.5rem;
        margin-top: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .menu-hero {
        padding-top: calc(var(--spacing-lg) + 55px);
    }
    
    .menu-title {
        font-size: 2rem;
        margin-top: var(--spacing-md);
    }
}
